{"inputs": [{"path": "CMakeLists.txt"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeDetermineSystem.cmake"}, {"isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build/cmake/ohos.toolchain.cmake"}, {"isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build/cmake/sdk_native_platforms.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeSystem.cmake.in"}, {"isGenerated": true, "path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/.cxx/default/default/arm64-v8a/CMakeFiles/3.16.5/CMakeSystem.cmake"}, {"isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build/cmake/ohos.toolchain.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeDetermineCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeDetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeDetermineCompilerId.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeCompilerIdDetection.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/ADSP-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/ARMCC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/ARMClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/AppleClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Borland-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Bruce-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Clang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Compaq-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Cray-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Embarcadero-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Fujitsu-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/GHS-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/GNU-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/HP-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/IAR-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Intel-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/MSVC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/NVIDIA-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/PGI-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/PathScale-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/SCO-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/SDCC-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/SunPro-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/TI-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Watcom-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/XL-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/XLClang-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/zOS-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeFindBinUtils.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Clang-FindBinUtils.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeCCompiler.cmake.in"}, {"isGenerated": true, "path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/.cxx/default/default/arm64-v8a/CMakeFiles/3.16.5/CMakeCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeDetermineCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeDetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeDetermineCompilerId.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeCompilerIdDetection.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/ADSP-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/ARMCC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/ARMClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/AppleClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Borland-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Clang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Cray-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Embarcadero-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Fujitsu-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/GHS-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/HP-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/IAR-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Intel-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/MSVC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/NVIDIA-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/PGI-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/PathScale-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/SCO-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/TI-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Watcom-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/XL-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeFindBinUtils.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Clang-FindBinUtils.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeCXXCompiler.cmake.in"}, {"isGenerated": true, "path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/.cxx/default/default/arm64-v8a/CMakeFiles/3.16.5/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Platform/OHOS.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Platform/Linux.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Platform/UnixPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Clang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Internal/CMakeCheckCompilerFlag.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Platform/OHOS.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Platform/Linux.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Platform/UnixPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeTestCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeTestCompilerCommon.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeDetermineCompilerABI.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeParseImplicitIncludeInfo.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeParseImplicitLinkInfo.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeTestCompilerCommon.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeCCompilerABI.c"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeDetermineCompileFeatures.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Internal/FeatureTesting.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeCCompiler.cmake.in"}, {"isGenerated": true, "path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/.cxx/default/default/arm64-v8a/CMakeFiles/3.16.5/CMakeCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Clang-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Platform/OHOS.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Platform/Linux.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Platform/UnixPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeTestCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeTestCompilerCommon.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeDetermineCompilerABI.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeParseImplicitIncludeInfo.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeParseImplicitLinkInfo.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeTestCompilerCommon.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeCXXCompilerABI.cpp"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeDetermineCompileFeatures.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Internal/FeatureTesting.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeCXXCompiler.cmake.in"}, {"isGenerated": true, "path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/.cxx/default/default/arm64-v8a/CMakeFiles/3.16.5/CMakeCXXCompiler.cmake"}, {"path": "ijksdl/CMakeLists.txt"}, {"path": "ijkplayer/CMakeLists.txt"}], "kind": "cmakeFiles", "paths": {"build": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/.cxx/default/default/arm64-v8a", "source": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp"}, "version": {"major": 1, "minor": 0}}