{"cmake": {"generator": {"name": "Ninja"}, "paths": {"cmake": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/bin/cmake.exe", "cpack": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/bin/cpack.exe", "ctest": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/bin/ctest.exe", "root": "D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16"}, "version": {"isDirty": false, "major": 3, "minor": 16, "patch": 5, "string": "3.16.5", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-c8d41063f79ca25db94c.json", "kind": "codemodel", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-9aa9cd970dd27aeaa85c.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-9aa9cd970dd27aeaa85c.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-c8d41063f79ca25db94c.json", "kind": "codemodel", "version": {"major": 2, "minor": 0}}}}