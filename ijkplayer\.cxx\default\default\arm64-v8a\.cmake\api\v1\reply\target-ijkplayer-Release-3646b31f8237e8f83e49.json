{"artifacts": [{"path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/build/default/intermediates/cmake/default/obj/arm64-v8a/libijkplayer.so"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_directories", "target_link_libraries", "add_definitions", "include_directories"], "files": ["ijkplayer/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 10, "parent": 0}, {"command": 1, "file": 0, "line": 61, "parent": 0}, {"command": 2, "file": 0, "line": 64, "parent": 0}, {"command": 2, "file": 0, "line": 65, "parent": 0}, {"command": 2, "file": 0, "line": 66, "parent": 0}, {"command": 2, "file": 0, "line": 67, "parent": 0}, {"command": 2, "file": 0, "line": 68, "parent": 0}, {"command": 2, "file": 0, "line": 69, "parent": 0}, {"command": 2, "file": 0, "line": 70, "parent": 0}, {"command": 2, "file": 0, "line": 71, "parent": 0}, {"command": 2, "file": 0, "line": 72, "parent": 0}, {"command": 2, "file": 0, "line": 73, "parent": 0}, {"command": 2, "file": 0, "line": 74, "parent": 0}, {"command": 2, "file": 0, "line": 75, "parent": 0}, {"command": 2, "file": 0, "line": 76, "parent": 0}, {"command": 2, "file": 0, "line": 77, "parent": 0}, {"command": 2, "file": 0, "line": 78, "parent": 0}, {"command": 2, "file": 0, "line": 79, "parent": 0}, {"command": 2, "file": 0, "line": 80, "parent": 0}, {"command": 2, "file": 0, "line": 81, "parent": 0}, {"command": 2, "file": 0, "line": 82, "parent": 0}, {"command": 2, "file": 0, "line": 83, "parent": 0}, {"command": 3, "file": 0, "line": 8, "parent": 0}, {"command": 4, "file": 0, "line": 52, "parent": 0}, {"command": 4, "file": 0, "line": 53, "parent": 0}, {"command": 4, "file": 0, "line": 54, "parent": 0}, {"command": 4, "file": 0, "line": 55, "parent": 0}, {"command": 4, "file": 0, "line": 56, "parent": 0}, {"command": 4, "file": 0, "line": 57, "parent": 0}, {"command": 4, "file": 0, "line": 58, "parent": 0}, {"command": 4, "file": 0, "line": 59, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC  "}], "defines": [{"backtrace": 23, "define": "OHOS_PLATFORM"}, {"define": "ijkplayer_EXPORTS"}], "includes": [{"backtrace": 24, "path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer"}, {"backtrace": 25, "path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg"}, {"backtrace": 26, "path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include"}, {"backtrace": 27, "path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include"}, {"backtrace": 28, "path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/arm64-v8a/include"}, {"backtrace": 29, "path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/arm64-v8a/include"}, {"backtrace": 30, "path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/arm64-v8a/include"}, {"backtrace": 31, "path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/arm64-v8a/include"}], "language": "C", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26], "sysroot": {"path": "D:/harmonyFor/openSDK/11/native/sysroot"}}, {"compileCommandFragments": [{"fragment": "-fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security  -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -O2 -DNDEBUG  -fPIC  "}], "defines": [{"backtrace": 23, "define": "OHOS_PLATFORM"}, {"define": "ijkplayer_EXPORTS"}], "includes": [{"backtrace": 24, "path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer"}, {"backtrace": 25, "path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg"}, {"backtrace": 26, "path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include"}, {"backtrace": 27, "path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include"}, {"backtrace": 28, "path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/arm64-v8a/include"}, {"backtrace": 29, "path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/arm64-v8a/include"}, {"backtrace": 30, "path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/arm64-v8a/include"}, {"backtrace": 31, "path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/arm64-v8a/include"}], "language": "CXX", "sourceIndexes": [27, 28, 29, 30, 31], "sysroot": {"path": "D:/harmonyFor/openSDK/11/native/sysroot"}}], "id": "ijkplayer::@01c62b87abfce31ea8e9", "link": {"commandFragments": [{"fragment": "--rtlib=compiler-rt -fuse-ld=lld -Wl,--build-id=sha1 -Wl,--warn-shared-textrel -Wl,--fatal-warnings -lunwind -Wl,--no-undefined -Qunused-arguments -Wl,-z,noexecstack", "role": "flags"}, {"backtrace": 2, "fragment": "-LD:\\vp8\\ohos_ijkplayer-vp8\\ijkplayer\\src\\main\\cpp\\ijkplayer\\..\\third_party\\ffmpeg\\ffmpeg\\arm64-v8a\\lib", "role": "libraryPath"}, {"backtrace": 2, "fragment": "-LD:\\vp8\\ohos_ijkplayer-vp8\\ijkplayer\\src\\main\\cpp\\ijkplayer\\..\\third_party\\openssl\\arm64-v8a\\lib", "role": "libraryPath"}, {"fragment": "-Wl,-rpath,D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/arm64-v8a/lib:D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/arm64-v8a/lib:D:/vp8/ohos_ijkplayer-vp8/ijkplayer/build/default/intermediates/cmake/default/obj/arm64-v8a", "role": "libraries"}, {"backtrace": 3, "fragment": "D:\\vp8\\ohos_ijkplayer-vp8\\ijkplayer\\build\\default\\intermediates\\cmake\\default\\obj\\arm64-v8a\\libijksdl.so", "role": "libraries"}, {"backtrace": 4, "fragment": "-lEGL", "role": "libraries"}, {"backtrace": 5, "fragment": "-lGLESv3", "role": "libraries"}, {"backtrace": 6, "fragment": "-lhilog_ndk.z", "role": "libraries"}, {"backtrace": 7, "fragment": "D:\\vp8\\ohos_ijkplayer-vp8\\ijkplayer\\src\\main\\cpp\\ijkplayer\\..\\third_party\\soundtouch\\arm64-v8a\\lib\\libsoundtouch.a", "role": "libraries"}, {"backtrace": 8, "fragment": "-lz", "role": "libraries"}, {"backtrace": 9, "fragment": "-lavcodec", "role": "libraries"}, {"backtrace": 10, "fragment": "-lavfilter", "role": "libraries"}, {"backtrace": 11, "fragment": "-lavformat", "role": "libraries"}, {"backtrace": 12, "fragment": "-lavu<PERSON>", "role": "libraries"}, {"backtrace": 13, "fragment": "-lswresample", "role": "libraries"}, {"backtrace": 14, "fragment": "-lswscale", "role": "libraries"}, {"backtrace": 15, "fragment": "-lavde<PERSON>", "role": "libraries"}, {"backtrace": 16, "fragment": "-lcrypto", "role": "libraries"}, {"backtrace": 17, "fragment": "-lssl", "role": "libraries"}, {"backtrace": 18, "fragment": "-lnative_media_codecbase", "role": "libraries"}, {"backtrace": 19, "fragment": "-lnative_media_core", "role": "libraries"}, {"backtrace": 20, "fragment": "-lnative_media_vdec", "role": "libraries"}, {"backtrace": 21, "fragment": "-lnative_window", "role": "libraries"}, {"backtrace": 22, "fragment": "-lnative_buffer", "role": "libraries"}, {"fragment": "-lm", "role": "libraries"}], "language": "CXX", "sysroot": {"path": "D:/harmonyFor/openSDK/11/native/sysroot"}}, "name": "<PERSON><PERSON>player", "nameOnDisk": "libijkplayer.so", "paths": {"build": "<PERSON><PERSON>player", "source": "<PERSON><PERSON>player"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "ijkplayer/ff_cmdutils.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ijkplayer/ff_ffplay.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ijkplayer/ff_ffpipeline.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ijkplayer/ff_ffpipenode.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ijkplayer/ijkmeta.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ijkplayer/ijkplayer.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ijkplayer/ijkplayer_android.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ijkplayer/pipeline/ffpipenode_ffplay_vdec.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ijkplayer/pipeline/ffpipeline_android.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ijkplayer/ijkavformat/allformats.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ijkplayer/ijkavformat/ijklivehook.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ijkplayer/ijkavformat/ijkio.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ijkplayer/ijkavformat/ijkiomanager.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ijkplayer/ijkavformat/ijkiocache.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ijkplayer/ijkavformat/ijkioffio.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ijkplayer/ijkavformat/ijkioprotocol.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ijkplayer/ijkavformat/ijkioapplication.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ijkplayer/ijkavformat/ijkiourlhook.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ijkplayer/ijkavformat/ijkasync.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ijkplayer/ijkavformat/ijkurlhook.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ijkplayer/ijkavformat/ijklongurl.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ijkplayer/ijkavformat/ijksegment.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ijkplayer/ijkavutil/ijkdict.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ijkplayer/ijkavutil/ijkutils.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ijkplayer/ijkavutil/ijkthreadpool.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ijkplayer/ijkavutil/ijktree.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ijkplayer/ijkavutil/ijkfifo.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "ijkplayer/ijkavutil/ijkstl.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "ijkplayer/ohos/ffpipenode_ohos_mediacodec_vdec.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "ijkplayer/ohos/ohos_video_decoder_data.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "ijkplayer/ohos/ohos_video_decoder_Info.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "ijkplayer/ohos/ohos_video_decoder.cpp", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}