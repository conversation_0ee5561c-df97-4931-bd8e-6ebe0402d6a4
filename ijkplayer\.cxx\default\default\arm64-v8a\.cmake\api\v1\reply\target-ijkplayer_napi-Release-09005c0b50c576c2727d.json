{"artifacts": [{"path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/build/default/intermediates/cmake/default/obj/arm64-v8a/libijkplayer_napi.so"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "add_definitions", "include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 13, "parent": 0}, {"command": 1, "file": 0, "line": 41, "parent": 0}, {"command": 1, "file": 0, "line": 42, "parent": 0}, {"command": 1, "file": 0, "line": 43, "parent": 0}, {"command": 1, "file": 0, "line": 44, "parent": 0}, {"command": 1, "file": 0, "line": 45, "parent": 0}, {"command": 1, "file": 0, "line": 46, "parent": 0}, {"command": 1, "file": 0, "line": 47, "parent": 0}, {"command": 1, "file": 0, "line": 48, "parent": 0}, {"command": 2, "file": 0, "line": 11, "parent": 0}, {"command": 3, "file": 0, "line": 31, "parent": 0}, {"command": 3, "file": 0, "line": 32, "parent": 0}, {"command": 3, "file": 0, "line": 33, "parent": 0}, {"command": 3, "file": 0, "line": 34, "parent": 0}, {"command": 3, "file": 0, "line": 35, "parent": 0}, {"command": 3, "file": 0, "line": 36, "parent": 0}, {"command": 3, "file": 0, "line": 37, "parent": 0}, {"command": 3, "file": 0, "line": 38, "parent": 0}, {"command": 3, "file": 0, "line": 39, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security  -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -O2 -DNDEBUG  -fPIC  "}], "defines": [{"backtrace": 10, "define": "OHOS_PLATFORM"}, {"define": "ijkplayer_napi_EXPORTS"}], "includes": [{"backtrace": 11, "path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp"}, {"backtrace": 12, "path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer"}, {"backtrace": 13, "path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl"}, {"backtrace": 14, "path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/proxy"}, {"backtrace": 15, "path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/napi"}, {"backtrace": 16, "path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/utils/napi"}, {"backtrace": 17, "path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/third_party/ffmpeg"}, {"backtrace": 18, "path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/third_party/ffmpeg/ffmpeg/arm64-v8a/include"}, {"backtrace": 19, "path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/third_party/openssl/arm64-v8a/include"}], "language": "CXX", "sourceIndexes": [0, 1, 2, 3, 6], "sysroot": {"path": "D:/harmonyFor/openSDK/11/native/sysroot"}}, {"compileCommandFragments": [{"fragment": "-fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -O2 -DNDEBUG  -fPIC  "}], "defines": [{"backtrace": 10, "define": "OHOS_PLATFORM"}, {"define": "ijkplayer_napi_EXPORTS"}], "includes": [{"backtrace": 11, "path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp"}, {"backtrace": 12, "path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer"}, {"backtrace": 13, "path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl"}, {"backtrace": 14, "path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/proxy"}, {"backtrace": 15, "path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/napi"}, {"backtrace": 16, "path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/utils/napi"}, {"backtrace": 17, "path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/third_party/ffmpeg"}, {"backtrace": 18, "path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/third_party/ffmpeg/ffmpeg/arm64-v8a/include"}, {"backtrace": 19, "path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/third_party/openssl/arm64-v8a/include"}], "language": "C", "sourceIndexes": [4, 5], "sysroot": {"path": "D:/harmonyFor/openSDK/11/native/sysroot"}}], "dependencies": [{"backtrace": 3, "id": "ijksdl::@1a6ca5e9b59ff8a0b610"}, {"backtrace": 2, "id": "ijkplayer::@01c62b87abfce31ea8e9"}], "id": "ijkplayer_napi::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "--rtlib=compiler-rt -fuse-ld=lld -Wl,--build-id=sha1 -Wl,--warn-shared-textrel -Wl,--fatal-warnings -lunwind -Wl,--no-undefined -Qunused-arguments -Wl,-z,noexecstack", "role": "flags"}, {"backtrace": 2, "fragment": "-LD:\\vp8\\ohos_ijkplayer-vp8\\ijkplayer\\src\\main\\cpp\\ijkplayer\\..\\third_party\\ffmpeg\\ffmpeg\\arm64-v8a\\lib", "role": "libraryPath"}, {"backtrace": 2, "fragment": "-LD:\\vp8\\ohos_ijkplayer-vp8\\ijkplayer\\src\\main\\cpp\\ijkplayer\\..\\third_party\\openssl\\arm64-v8a\\lib", "role": "libraryPath"}, {"backtrace": 3, "fragment": "-LD:\\vp8\\ohos_ijkplayer-vp8\\ijkplayer\\src\\main\\cpp\\ijksdl\\..\\third_party\\ffmpeg\\ffmpeg\\arm64-v8a\\lib", "role": "libraryPath"}, {"backtrace": 3, "fragment": "-LD:\\vp8\\ohos_ijkplayer-vp8\\ijkplayer\\src\\main\\cpp\\ijksdl\\..\\third_party\\openssl\\arm64-v8a\\lib", "role": "libraryPath"}, {"fragment": "-Wl,-rpath,D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/arm64-v8a/lib:D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/arm64-v8a/lib:D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/arm64-v8a/lib:D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/arm64-v8a/lib:D:/vp8/ohos_ijkplayer-vp8/ijkplayer/build/default/intermediates/cmake/default/obj/arm64-v8a", "role": "libraries"}, {"backtrace": 2, "fragment": "D:\\vp8\\ohos_ijkplayer-vp8\\ijkplayer\\build\\default\\intermediates\\cmake\\default\\obj\\arm64-v8a\\libijkplayer.so", "role": "libraries"}, {"backtrace": 3, "fragment": "D:\\vp8\\ohos_ijkplayer-vp8\\ijkplayer\\build\\default\\intermediates\\cmake\\default\\obj\\arm64-v8a\\libijksdl.so", "role": "libraries"}, {"backtrace": 4, "fragment": "-lEGL", "role": "libraries"}, {"backtrace": 5, "fragment": "-lGLESv3", "role": "libraries"}, {"backtrace": 6, "fragment": "-lhilog_ndk.z", "role": "libraries"}, {"backtrace": 7, "fragment": "-lace_ndk.z", "role": "libraries"}, {"backtrace": 8, "fragment": "-lace_napi.z", "role": "libraries"}, {"backtrace": 9, "fragment": "-luv", "role": "libraries"}, {"fragment": "D:\\vp8\\ohos_ijkplayer-vp8\\ijkplayer\\build\\default\\intermediates\\cmake\\default\\obj\\arm64-v8a\\libijksdl.so", "role": "libraries"}, {"fragment": "D:\\vp8\\ohos_ijkplayer-vp8\\ijkplayer\\src\\main\\cpp\\ijkplayer\\..\\third_party\\soundtouch\\arm64-v8a\\lib\\libsoundtouch.a", "role": "libraries"}, {"fragment": "-lnative_media_codecbase", "role": "libraries"}, {"fragment": "-lnative_media_core", "role": "libraries"}, {"fragment": "-lnative_media_vdec", "role": "libraries"}, {"fragment": "-lnative_window", "role": "libraries"}, {"fragment": "-lnative_buffer", "role": "libraries"}, {"fragment": "D:\\vp8\\ohos_ijkplayer-vp8\\ijkplayer\\src\\main\\cpp\\ijksdl\\..\\third_party\\yuv\\arm64-v8a\\lib\\libyuv.a", "role": "libraries"}, {"fragment": "-lnative_window", "role": "libraries"}, {"fragment": "-lz", "role": "libraries"}, {"fragment": "-lavcodec", "role": "libraries"}, {"fragment": "-lavfilter", "role": "libraries"}, {"fragment": "-lavformat", "role": "libraries"}, {"fragment": "-lavu<PERSON>", "role": "libraries"}, {"fragment": "-lswresample", "role": "libraries"}, {"fragment": "-lswscale", "role": "libraries"}, {"fragment": "-lavde<PERSON>", "role": "libraries"}, {"fragment": "-lcrypto", "role": "libraries"}, {"fragment": "-lssl", "role": "libraries"}, {"fragment": "-<PERSON><PERSON><PERSON><PERSON>", "role": "libraries"}, {"fragment": "-lm", "role": "libraries"}], "language": "CXX", "sysroot": {"path": "D:/harmonyFor/openSDK/11/native/sysroot"}}, "name": "ijkplayer_napi", "nameOnDisk": "libijkplayer_napi.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "napi/ijkplayer_napi_init.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "napi/ijkplayer_napi.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "napi/ijkplayer_napi_manager.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "proxy/ijkplayer_napi_proxy.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "utils/hashmap/data_struct.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "utils/ffmpeg/custom_ffmpeg_log.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "utils/napi/napi_utils.cpp", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}