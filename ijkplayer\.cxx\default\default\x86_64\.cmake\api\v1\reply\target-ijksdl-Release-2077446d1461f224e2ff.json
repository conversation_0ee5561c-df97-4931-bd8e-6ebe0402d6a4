{"artifacts": [{"path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/build/default/intermediates/cmake/default/obj/x86_64/libijksdl.so"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_directories", "target_link_libraries", "add_definitions", "include_directories"], "files": ["ijksdl/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 10, "parent": 0}, {"command": 1, "file": 0, "line": 57, "parent": 0}, {"command": 2, "file": 0, "line": 60, "parent": 0}, {"command": 2, "file": 0, "line": 61, "parent": 0}, {"command": 2, "file": 0, "line": 62, "parent": 0}, {"command": 2, "file": 0, "line": 63, "parent": 0}, {"command": 2, "file": 0, "line": 64, "parent": 0}, {"command": 2, "file": 0, "line": 65, "parent": 0}, {"command": 2, "file": 0, "line": 66, "parent": 0}, {"command": 2, "file": 0, "line": 67, "parent": 0}, {"command": 2, "file": 0, "line": 68, "parent": 0}, {"command": 2, "file": 0, "line": 69, "parent": 0}, {"command": 2, "file": 0, "line": 70, "parent": 0}, {"command": 2, "file": 0, "line": 71, "parent": 0}, {"command": 2, "file": 0, "line": 72, "parent": 0}, {"command": 2, "file": 0, "line": 73, "parent": 0}, {"command": 2, "file": 0, "line": 74, "parent": 0}, {"command": 2, "file": 0, "line": 75, "parent": 0}, {"command": 3, "file": 0, "line": 8, "parent": 0}, {"command": 4, "file": 0, "line": 48, "parent": 0}, {"command": 4, "file": 0, "line": 49, "parent": 0}, {"command": 4, "file": 0, "line": 50, "parent": 0}, {"command": 4, "file": 0, "line": 51, "parent": 0}, {"command": 4, "file": 0, "line": 52, "parent": 0}, {"command": 4, "file": 0, "line": 53, "parent": 0}, {"command": 4, "file": 0, "line": 54, "parent": 0}, {"command": 4, "file": 0, "line": 55, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC  "}], "defines": [{"backtrace": 19, "define": "OHOS_PLATFORM"}, {"define": "ijksdl_EXPORTS"}], "includes": [{"backtrace": 20, "path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl"}, {"backtrace": 21, "path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg"}, {"backtrace": 22, "path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include"}, {"backtrace": 23, "path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include"}, {"backtrace": 24, "path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/x86_64/include"}, {"backtrace": 25, "path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/x86_64/include"}, {"backtrace": 26, "path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/x86_64/include"}, {"backtrace": 27, "path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/x86_64/include"}], "language": "C", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26], "sysroot": {"path": "D:/harmonyFor/openSDK/11/native/sysroot"}}, {"compileCommandFragments": [{"fragment": "-fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security  -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -O2 -DNDEBUG  -fPIC  "}], "defines": [{"backtrace": 19, "define": "OHOS_PLATFORM"}, {"define": "ijksdl_EXPORTS"}], "includes": [{"backtrace": 20, "path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl"}, {"backtrace": 21, "path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg"}, {"backtrace": 22, "path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include"}, {"backtrace": 23, "path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include"}, {"backtrace": 24, "path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/x86_64/include"}, {"backtrace": 25, "path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/x86_64/include"}, {"backtrace": 26, "path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/x86_64/include"}, {"backtrace": 27, "path": "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/x86_64/include"}], "language": "CXX", "sourceIndexes": [27], "sysroot": {"path": "D:/harmonyFor/openSDK/11/native/sysroot"}}], "id": "ijksdl::@1a6ca5e9b59ff8a0b610", "link": {"commandFragments": [{"fragment": "--rtlib=compiler-rt -fuse-ld=lld -Wl,--build-id=sha1 -Wl,--warn-shared-textrel -Wl,--fatal-warnings -lunwind -Wl,--no-undefined -Qunused-arguments -Wl,-z,noexecstack", "role": "flags"}, {"backtrace": 2, "fragment": "-LD:\\vp8\\ohos_ijkplayer-vp8\\ijkplayer\\src\\main\\cpp\\ijksdl\\..\\third_party\\ffmpeg\\ffmpeg\\x86_64\\lib", "role": "libraryPath"}, {"backtrace": 2, "fragment": "-LD:\\vp8\\ohos_ijkplayer-vp8\\ijkplayer\\src\\main\\cpp\\ijksdl\\..\\third_party\\openssl\\x86_64\\lib", "role": "libraryPath"}, {"fragment": "-Wl,-r<PERSON>,D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/x86_64/lib:D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/x86_64/lib", "role": "libraries"}, {"backtrace": 3, "fragment": "D:\\vp8\\ohos_ijkplayer-vp8\\ijkplayer\\src\\main\\cpp\\ijksdl\\..\\third_party\\yuv\\x86_64\\lib\\libyuv.a", "role": "libraries"}, {"backtrace": 4, "fragment": "-lEGL", "role": "libraries"}, {"backtrace": 5, "fragment": "-lGLESv3", "role": "libraries"}, {"backtrace": 6, "fragment": "-lhilog_ndk.z", "role": "libraries"}, {"backtrace": 7, "fragment": "-lnative_window", "role": "libraries"}, {"backtrace": 8, "fragment": "-lz", "role": "libraries"}, {"backtrace": 9, "fragment": "-lavcodec", "role": "libraries"}, {"backtrace": 10, "fragment": "-lavfilter", "role": "libraries"}, {"backtrace": 11, "fragment": "-lavformat", "role": "libraries"}, {"backtrace": 12, "fragment": "-lavu<PERSON>", "role": "libraries"}, {"backtrace": 13, "fragment": "-lswresample", "role": "libraries"}, {"backtrace": 14, "fragment": "-lswscale", "role": "libraries"}, {"backtrace": 15, "fragment": "-lavde<PERSON>", "role": "libraries"}, {"backtrace": 16, "fragment": "-lcrypto", "role": "libraries"}, {"backtrace": 17, "fragment": "-lssl", "role": "libraries"}, {"backtrace": 18, "fragment": "-<PERSON><PERSON><PERSON><PERSON>", "role": "libraries"}, {"fragment": "-lm", "role": "libraries"}], "language": "CXX", "sysroot": {"path": "D:/harmonyFor/openSDK/11/native/sysroot"}}, "name": "ijksdl", "nameOnDisk": "libijksdl.so", "paths": {"build": "ijksdl", "source": "ijksdl"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "ijksdl/ijksdl_aout.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ijksdl/ijksdl_audio.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ijksdl/ijksdl_egl.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ijksdl/ijksdl_error.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ijksdl/ijksdl_mutex.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ijksdl/ijksdl_stdinc.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ijksdl/ijksdl_thread.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ijksdl/ijksdl_timer.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ijksdl/ijksdl_vout.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ijksdl/ijksdl_extra_log.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ijksdl/video/gles2/color.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ijksdl/video/gles2/common.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ijksdl/video/gles2/renderer.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ijksdl/video/gles2/renderer_rgb.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ijksdl/video/gles2/renderer_yuv420p.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ijksdl/video/gles2/renderer_yuv444p10le.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ijksdl/video/gles2/shader.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ijksdl/video/gles2/fsh/rgb.fsh.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ijksdl/video/gles2/fsh/yuv420p.fsh.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ijksdl/video/gles2/fsh/yuv444p10le.fsh.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ijksdl/video/gles2/vsh/mvp.vsh.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ijksdl/video/ijksdl_vout_android_nativewindow.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ijksdl/video/ijksdl_vout_android_surface.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ijksdl/ffmpeg/ijksdl_vout_overlay_ffmpeg.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ijksdl/ffmpeg/abi_all/image_convert.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ijksdl/audio/ijksdl_aout_android_opensles.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "ijksdl/dummy/ijksdl_vout_dummy.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "utils/ohoslog/ohos_log.cpp", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}