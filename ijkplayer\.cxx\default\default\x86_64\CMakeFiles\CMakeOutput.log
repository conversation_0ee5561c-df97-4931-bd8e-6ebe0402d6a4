The target system is: OHOS - 1 - x86_64
The host system is: Windows - 10.0.19045 - AMD64
Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
Compiler: D:/harmonyFor/openSDK/11/native/llvm/bin/clang.exe 
Build flags: -fdata-sections;-ffunction-sections;-funwind-tables;-fstack-protector-strong;-no-canonical-prefixes;-fno-addrsig;-Wa,--noexecstack;-Wformat;-Werror=format-security;-s;-D__MUSL__
Id flags: -c;--target=x86_64-linux-ohos 

The output was:
0
clang: warning: argument unused during compilation: '-s' [-Wunused-command-line-argument]


Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CMakeCCompilerId.o"

The C compiler identification is Clang, found in "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/.cxx/default/default/x86_64/CMakeFiles/3.16.5/CompilerIdC/CMakeCCompilerId.o"

Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
Compiler: D:/harmonyFor/openSDK/11/native/llvm/bin/clang++.exe 
Build flags: -fdata-sections;-ffunction-sections;-funwind-tables;-fstack-protector-strong;-no-canonical-prefixes;-fno-addrsig;-Wa,--noexecstack;-Wformat;-Werror=format-security;;-s;-D__MUSL__
Id flags: -c;--target=x86_64-linux-ohos 

The output was:
0
clang++: warning: argument unused during compilation: '-s' [-Wunused-command-line-argument]


Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CMakeCXXCompilerId.o"

The CXX compiler identification is Clang, found in "D:/vp8/ohos_ijkplayer-vp8/ijkplayer/.cxx/default/default/x86_64/CMakeFiles/3.16.5/CompilerIdCXX/CMakeCXXCompilerId.o"

Determining if the C compiler works passed with the following output:
Change Dir: D:/vp8/ohos_ijkplayer-vp8/ijkplayer/.cxx/default/default/x86_64/CMakeFiles/CMakeTmp

Run Build Command(s):D:\harmonyFor\openSDK\11\native\build-tools\cmake\bin\ninja.exe cmTC_135fa && [1/2] Building C object CMakeFiles/cmTC_135fa.dir/testCCompiler.c.o

clang: warning: argument unused during compilation: '--gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm' [-Wunused-command-line-argument]

clang: warning: argument unused during compilation: '-s' [-Wunused-command-line-argument]

[2/2] Linking C executable cmTC_135fa




Detecting C compiler ABI info compiled with the following output:
Change Dir: D:/vp8/ohos_ijkplayer-vp8/ijkplayer/.cxx/default/default/x86_64/CMakeFiles/CMakeTmp

Run Build Command(s):D:\harmonyFor\openSDK\11\native\build-tools\cmake\bin\ninja.exe cmTC_9a253 && [1/2] Building C object CMakeFiles/cmTC_9a253.dir/CMakeCCompilerABI.c.o

OHOS (dev) clang version 15.0.4 (llvm-project 81cdec3cd117b1e6e3a9f1ebc4695d790c978463)

Target: x86_64-unknown-linux-ohos

Thread model: posix

InstalledDir: D:/harmonyFor/openSDK/11/native/llvm/bin

clang: warning: argument unused during compilation: '--gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm' [-Wunused-command-line-argument]

clang: warning: argument unused during compilation: '-s' [-Wunused-command-line-argument]

 (in-process)

 "D:/harmonyFor/openSDK/11/native/llvm/bin/clang.exe" -cc1 -triple x86_64-unknown-linux-ohos -emit-obj -mrelax-all --mrelax-relocations -mnoexecstack -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCCompilerABI.c -mrelocation-model pic -pic-level 2 -pic-is-pie -mframe-pointer=all -ffp-contract=on -fno-rounding-math -mconstructor-aliases -funwind-tables=1 -target-cpu x86-64 -tune-cpu generic -mllvm -treat-scalable-fixed-error-as-warning -debugger-tuning=gdb -v -ffunction-sections -fdata-sections -fcoverage-compilation-dir=D:/vp8/ohos_ijkplayer-vp8/ijkplayer/.cxx/default/default/x86_64/CMakeFiles/CMakeTmp -resource-dir D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4 -D __MUSL__ -isysroot D:/harmonyFor/openSDK/11/native/sysroot -internal-isystem D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/include -internal-externc-isystem D:/harmonyFor/openSDK/11/native/sysroot/usr/include/x86_64-linux-ohos -internal-externc-isystem D:/harmonyFor/openSDK/11/native/sysroot/include -internal-externc-isystem D:/harmonyFor/openSDK/11/native/sysroot/usr/include -Wformat -Werror=format-security -fdebug-compilation-dir=D:/vp8/ohos_ijkplayer-vp8/ijkplayer/.cxx/default/default/x86_64/CMakeFiles/CMakeTmp -ferror-limit 19 -stack-protector 2 -fgnuc-version=4.2.1 -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_9a253.dir/CMakeCCompilerABI.c.o -x c D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeCCompilerABI.c

clang -cc1 version 15.0.4 based upon LLVM 15.0.4 default target x86_64-w64-windows-gnu

ignoring nonexistent directory "D:/harmonyFor/openSDK/11/native/sysroot/include"

#include "..." search starts here:

#include <...> search starts here:

 D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/include

 D:/harmonyFor/openSDK/11/native/sysroot/usr/include/x86_64-linux-ohos

 D:/harmonyFor/openSDK/11/native/sysroot/usr/include

End of search list.

[2/2] Linking C executable cmTC_9a253

OHOS (dev) clang version 15.0.4 (llvm-project 81cdec3cd117b1e6e3a9f1ebc4695d790c978463)

Target: x86_64-unknown-linux-ohos

Thread model: posix

InstalledDir: D:/harmonyFor/openSDK/11/native/llvm/bin

 "D:/harmonyFor/openSDK/11/native/llvm/bin/ld.lld" --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -pie -s -z now -z relro -z max-page-size=4096 --hash-style=gnu --hash-style=both --enable-new-dtags --eh-frame-hdr -m elf_x86_64 -dynamic-linker /lib/ld-musl-x86_64.so.1 -o cmTC_9a253 D:/harmonyFor/openSDK/11/native/sysroot/usr/lib/x86_64-linux-ohos/Scrt1.o D:/harmonyFor/openSDK/11/native/sysroot/usr/lib/x86_64-linux-ohos/crti.o D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/lib/x86_64-linux-ohos/clang_rt.crtbegin.o -LD:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/lib/x86_64-linux-ohos -LD:/harmonyFor/openSDK/11/native/sysroot/usr/lib/ -LD:/harmonyFor/openSDK/11/native/llvm/bin/../lib/x86_64-linux-ohos/ -LD:/harmonyFor/openSDK/11/native/sysroot/usr/lib/x86_64-linux-ohos/ --build-id=sha1 --warn-shared-textrel --fatal-warnings -lunwind --no-undefined -z noexecstack --gc-sections CMakeFiles/cmTC_9a253.dir/CMakeCCompilerABI.c.o D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/lib/x86_64-linux-ohos/libclang_rt.builtins.a -l:libunwind.a -lc D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/lib/x86_64-linux-ohos/libclang_rt.builtins.a -l:libunwind.a D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/lib/x86_64-linux-ohos/clang_rt.crtend.o D:/harmonyFor/openSDK/11/native/sysroot/usr/lib/x86_64-linux-ohos/crtn.o




Parsed C implicit include dir info from above output: rv=done
  found start of include info
  found start of implicit include info
    add: [D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/include]
    add: [D:/harmonyFor/openSDK/11/native/sysroot/usr/include/x86_64-linux-ohos]
    add: [D:/harmonyFor/openSDK/11/native/sysroot/usr/include]
  end of search list found
  collapse include dir [D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/include] ==> [D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/include]
  collapse include dir [D:/harmonyFor/openSDK/11/native/sysroot/usr/include/x86_64-linux-ohos] ==> [D:/harmonyFor/openSDK/11/native/sysroot/usr/include/x86_64-linux-ohos]
  collapse include dir [D:/harmonyFor/openSDK/11/native/sysroot/usr/include] ==> [D:/harmonyFor/openSDK/11/native/sysroot/usr/include]
  implicit include dirs: [D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/include;D:/harmonyFor/openSDK/11/native/sysroot/usr/include/x86_64-linux-ohos;D:/harmonyFor/openSDK/11/native/sysroot/usr/include]


Parsed C implicit link information from above output:
  link line regex: [^( *|.*[/\])(ld\.lld\.exe|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\]+-)?ld|collect2)[^/\]*( |$)]
  ignore line: [Change Dir: D:/vp8/ohos_ijkplayer-vp8/ijkplayer/.cxx/default/default/x86_64/CMakeFiles/CMakeTmp]
  ignore line: []
  ignore line: [Run Build Command(s):D:\harmonyFor\openSDK\11\native\build-tools\cmake\bin\ninja.exe cmTC_9a253 && [1/2] Building C object CMakeFiles/cmTC_9a253.dir/CMakeCCompilerABI.c.o]
  ignore line: [OHOS (dev) clang version 15.0.4 (llvm-project 81cdec3cd117b1e6e3a9f1ebc4695d790c978463)]
  ignore line: [Target: x86_64-unknown-linux-ohos]
  ignore line: [Thread model: posix]
  ignore line: [InstalledDir: D:/harmonyFor/openSDK/11/native/llvm/bin]
  ignore line: [clang: warning: argument unused during compilation: '--gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm' [-Wunused-command-line-argument]]
  ignore line: [clang: warning: argument unused during compilation: '-s' [-Wunused-command-line-argument]]
  ignore line: [ (in-process)]
  ignore line: [ "D:/harmonyFor/openSDK/11/native/llvm/bin/clang.exe" -cc1 -triple x86_64-unknown-linux-ohos -emit-obj -mrelax-all --mrelax-relocations -mnoexecstack -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCCompilerABI.c -mrelocation-model pic -pic-level 2 -pic-is-pie -mframe-pointer=all -ffp-contract=on -fno-rounding-math -mconstructor-aliases -funwind-tables=1 -target-cpu x86-64 -tune-cpu generic -mllvm -treat-scalable-fixed-error-as-warning -debugger-tuning=gdb -v -ffunction-sections -fdata-sections -fcoverage-compilation-dir=D:/vp8/ohos_ijkplayer-vp8/ijkplayer/.cxx/default/default/x86_64/CMakeFiles/CMakeTmp -resource-dir D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4 -D __MUSL__ -isysroot D:/harmonyFor/openSDK/11/native/sysroot -internal-isystem D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/include -internal-externc-isystem D:/harmonyFor/openSDK/11/native/sysroot/usr/include/x86_64-linux-ohos -internal-externc-isystem D:/harmonyFor/openSDK/11/native/sysroot/include -internal-externc-isystem D:/harmonyFor/openSDK/11/native/sysroot/usr/include -Wformat -Werror=format-security -fdebug-compilation-dir=D:/vp8/ohos_ijkplayer-vp8/ijkplayer/.cxx/default/default/x86_64/CMakeFiles/CMakeTmp -ferror-limit 19 -stack-protector 2 -fgnuc-version=4.2.1 -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_9a253.dir/CMakeCCompilerABI.c.o -x c D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeCCompilerABI.c]
  ignore line: [clang -cc1 version 15.0.4 based upon LLVM 15.0.4 default target x86_64-w64-windows-gnu]
  ignore line: [ignoring nonexistent directory "D:/harmonyFor/openSDK/11/native/sysroot/include"]
  ignore line: [#include "..." search starts here:]
  ignore line: [#include <...> search starts here:]
  ignore line: [ D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/include]
  ignore line: [ D:/harmonyFor/openSDK/11/native/sysroot/usr/include/x86_64-linux-ohos]
  ignore line: [ D:/harmonyFor/openSDK/11/native/sysroot/usr/include]
  ignore line: [End of search list.]
  ignore line: [[2/2] Linking C executable cmTC_9a253]
  ignore line: [OHOS (dev) clang version 15.0.4 (llvm-project 81cdec3cd117b1e6e3a9f1ebc4695d790c978463)]
  ignore line: [Target: x86_64-unknown-linux-ohos]
  ignore line: [Thread model: posix]
  ignore line: [InstalledDir: D:/harmonyFor/openSDK/11/native/llvm/bin]
  link line: [ "D:/harmonyFor/openSDK/11/native/llvm/bin/ld.lld" --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -pie -s -z now -z relro -z max-page-size=4096 --hash-style=gnu --hash-style=both --enable-new-dtags --eh-frame-hdr -m elf_x86_64 -dynamic-linker /lib/ld-musl-x86_64.so.1 -o cmTC_9a253 D:/harmonyFor/openSDK/11/native/sysroot/usr/lib/x86_64-linux-ohos/Scrt1.o D:/harmonyFor/openSDK/11/native/sysroot/usr/lib/x86_64-linux-ohos/crti.o D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/lib/x86_64-linux-ohos/clang_rt.crtbegin.o -LD:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/lib/x86_64-linux-ohos -LD:/harmonyFor/openSDK/11/native/sysroot/usr/lib/ -LD:/harmonyFor/openSDK/11/native/llvm/bin/../lib/x86_64-linux-ohos/ -LD:/harmonyFor/openSDK/11/native/sysroot/usr/lib/x86_64-linux-ohos/ --build-id=sha1 --warn-shared-textrel --fatal-warnings -lunwind --no-undefined -z noexecstack --gc-sections CMakeFiles/cmTC_9a253.dir/CMakeCCompilerABI.c.o D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/lib/x86_64-linux-ohos/libclang_rt.builtins.a -l:libunwind.a -lc D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/lib/x86_64-linux-ohos/libclang_rt.builtins.a -l:libunwind.a D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/lib/x86_64-linux-ohos/clang_rt.crtend.o D:/harmonyFor/openSDK/11/native/sysroot/usr/lib/x86_64-linux-ohos/crtn.o]
    arg [D:/harmonyFor/openSDK/11/native/llvm/bin/ld.lld] ==> ignore
    arg [--sysroot=D:/harmonyFor/openSDK/11/native/sysroot] ==> ignore
    arg [-pie] ==> ignore
    arg [-s] ==> ignore
    arg [-znow] ==> ignore
    arg [-zrelro] ==> ignore
    arg [-zmax-page-size=4096] ==> ignore
    arg [--hash-style=gnu] ==> ignore
    arg [--hash-style=both] ==> ignore
    arg [--enable-new-dtags] ==> ignore
    arg [--eh-frame-hdr] ==> ignore
    arg [-m] ==> ignore
    arg [elf_x86_64] ==> ignore
    arg [-dynamic-linker] ==> ignore
    arg [/lib/ld-musl-x86_64.so.1] ==> ignore
    arg [-o] ==> ignore
    arg [cmTC_9a253] ==> ignore
    arg [D:/harmonyFor/openSDK/11/native/sysroot/usr/lib/x86_64-linux-ohos/Scrt1.o] ==> ignore
    arg [D:/harmonyFor/openSDK/11/native/sysroot/usr/lib/x86_64-linux-ohos/crti.o] ==> ignore
    arg [D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/lib/x86_64-linux-ohos/clang_rt.crtbegin.o] ==> ignore
    arg [-LD:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/lib/x86_64-linux-ohos] ==> dir [D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/lib/x86_64-linux-ohos]
    arg [-LD:/harmonyFor/openSDK/11/native/sysroot/usr/lib/] ==> dir [D:/harmonyFor/openSDK/11/native/sysroot/usr/lib/]
    arg [-LD:/harmonyFor/openSDK/11/native/llvm/bin/../lib/x86_64-linux-ohos/] ==> dir [D:/harmonyFor/openSDK/11/native/llvm/bin/../lib/x86_64-linux-ohos/]
    arg [-LD:/harmonyFor/openSDK/11/native/sysroot/usr/lib/x86_64-linux-ohos/] ==> dir [D:/harmonyFor/openSDK/11/native/sysroot/usr/lib/x86_64-linux-ohos/]
    arg [--build-id=sha1] ==> ignore
    arg [--warn-shared-textrel] ==> ignore
    arg [--fatal-warnings] ==> ignore
    arg [-lunwind] ==> lib [unwind]
    arg [--no-undefined] ==> ignore
    arg [-znoexecstack] ==> ignore
    arg [--gc-sections] ==> ignore
    arg [CMakeFiles/cmTC_9a253.dir/CMakeCCompilerABI.c.o] ==> ignore
    arg [D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/lib/x86_64-linux-ohos/libclang_rt.builtins.a] ==> lib [D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/lib/x86_64-linux-ohos/libclang_rt.builtins.a]
    arg [-l:libunwind.a] ==> lib [-l:libunwind.a]
    arg [-lc] ==> lib [c]
    arg [D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/lib/x86_64-linux-ohos/libclang_rt.builtins.a] ==> lib [D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/lib/x86_64-linux-ohos/libclang_rt.builtins.a]
    arg [-l:libunwind.a] ==> lib [-l:libunwind.a]
    arg [D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/lib/x86_64-linux-ohos/clang_rt.crtend.o] ==> ignore
    arg [D:/harmonyFor/openSDK/11/native/sysroot/usr/lib/x86_64-linux-ohos/crtn.o] ==> ignore
  remove lib [D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/lib/x86_64-linux-ohos/libclang_rt.builtins.a]
  remove lib [D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/lib/x86_64-linux-ohos/libclang_rt.builtins.a]
  collapse library dir [D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/lib/x86_64-linux-ohos] ==> [D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/lib/x86_64-linux-ohos]
  collapse library dir [D:/harmonyFor/openSDK/11/native/sysroot/usr/lib/] ==> [D:/harmonyFor/openSDK/11/native/sysroot/usr/lib]
  collapse library dir [D:/harmonyFor/openSDK/11/native/llvm/bin/../lib/x86_64-linux-ohos/] ==> [D:/harmonyFor/openSDK/11/native/llvm/lib/x86_64-linux-ohos]
  collapse library dir [D:/harmonyFor/openSDK/11/native/sysroot/usr/lib/x86_64-linux-ohos/] ==> [D:/harmonyFor/openSDK/11/native/sysroot/usr/lib/x86_64-linux-ohos]
  implicit libs: [unwind;-l:libunwind.a;c;-l:libunwind.a]
  implicit dirs: [D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/lib/x86_64-linux-ohos;D:/harmonyFor/openSDK/11/native/sysroot/usr/lib;D:/harmonyFor/openSDK/11/native/llvm/lib/x86_64-linux-ohos;D:/harmonyFor/openSDK/11/native/sysroot/usr/lib/x86_64-linux-ohos]
  implicit fwks: []


Determining if the CXX compiler works passed with the following output:
Change Dir: D:/vp8/ohos_ijkplayer-vp8/ijkplayer/.cxx/default/default/x86_64/CMakeFiles/CMakeTmp

Run Build Command(s):D:\harmonyFor\openSDK\11\native\build-tools\cmake\bin\ninja.exe cmTC_23d14 && [1/2] Building CXX object CMakeFiles/cmTC_23d14.dir/testCXXCompiler.cxx.o

clang++: warning: argument unused during compilation: '--gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm' [-Wunused-command-line-argument]

clang++: warning: argument unused during compilation: '-s' [-Wunused-command-line-argument]

[2/2] Linking CXX executable cmTC_23d14




Detecting CXX compiler ABI info compiled with the following output:
Change Dir: D:/vp8/ohos_ijkplayer-vp8/ijkplayer/.cxx/default/default/x86_64/CMakeFiles/CMakeTmp

Run Build Command(s):D:\harmonyFor\openSDK\11\native\build-tools\cmake\bin\ninja.exe cmTC_a2958 && [1/2] Building CXX object CMakeFiles/cmTC_a2958.dir/CMakeCXXCompilerABI.cpp.o

OHOS (dev) clang version 15.0.4 (llvm-project 81cdec3cd117b1e6e3a9f1ebc4695d790c978463)

Target: x86_64-unknown-linux-ohos

Thread model: posix

InstalledDir: D:/harmonyFor/openSDK/11/native/llvm/bin

clang++: warning: argument unused during compilation: '--gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm' [-Wunused-command-line-argument]

clang++: warning: argument unused during compilation: '-s' [-Wunused-command-line-argument]

 (in-process)

 "D:/harmonyFor/openSDK/11/native/llvm/bin/clang++.exe" -cc1 -triple x86_64-unknown-linux-ohos -emit-obj -mrelax-all --mrelax-relocations -mnoexecstack -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCXXCompilerABI.cpp -mrelocation-model pic -pic-level 2 -pic-is-pie -mframe-pointer=all -ffp-contract=on -fno-rounding-math -mconstructor-aliases -funwind-tables=1 -target-cpu x86-64 -tune-cpu generic -mllvm -treat-scalable-fixed-error-as-warning -debugger-tuning=gdb -v -ffunction-sections -fdata-sections -fcoverage-compilation-dir=D:/vp8/ohos_ijkplayer-vp8/ijkplayer/.cxx/default/default/x86_64/CMakeFiles/CMakeTmp -resource-dir D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4 -D __MUSL__ -isysroot D:/harmonyFor/openSDK/11/native/sysroot -internal-isystem D:/harmonyFor/openSDK/11/native/llvm/bin/../include/libcxx-ohos/include/c++/v1 -internal-isystem D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/include -internal-externc-isystem D:/harmonyFor/openSDK/11/native/sysroot/usr/include/x86_64-linux-ohos -internal-externc-isystem D:/harmonyFor/openSDK/11/native/sysroot/include -internal-externc-isystem D:/harmonyFor/openSDK/11/native/sysroot/usr/include -Wformat -Werror=format-security -fdeprecated-macro -fdebug-compilation-dir=D:/vp8/ohos_ijkplayer-vp8/ijkplayer/.cxx/default/default/x86_64/CMakeFiles/CMakeTmp -ferror-limit 19 -stack-protector 2 -fgnuc-version=4.2.1 -fcxx-exceptions -fexceptions -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_a2958.dir/CMakeCXXCompilerABI.cpp.o -x c++ D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeCXXCompilerABI.cpp

clang -cc1 version 15.0.4 based upon LLVM 15.0.4 default target x86_64-w64-windows-gnu

ignoring nonexistent directory "D:/harmonyFor/openSDK/11/native/sysroot/include"

#include "..." search starts here:

#include <...> search starts here:

 D:/harmonyFor/openSDK/11/native/llvm/bin/../include/libcxx-ohos/include/c++/v1

 D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/include

 D:/harmonyFor/openSDK/11/native/sysroot/usr/include/x86_64-linux-ohos

 D:/harmonyFor/openSDK/11/native/sysroot/usr/include

End of search list.

[2/2] Linking CXX executable cmTC_a2958

OHOS (dev) clang version 15.0.4 (llvm-project 81cdec3cd117b1e6e3a9f1ebc4695d790c978463)

Target: x86_64-unknown-linux-ohos

Thread model: posix

InstalledDir: D:/harmonyFor/openSDK/11/native/llvm/bin

 "D:/harmonyFor/openSDK/11/native/llvm/bin/ld.lld" --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -pie -s -z now -z relro -z max-page-size=4096 --hash-style=gnu --hash-style=both --enable-new-dtags --eh-frame-hdr -m elf_x86_64 -dynamic-linker /lib/ld-musl-x86_64.so.1 -o cmTC_a2958 D:/harmonyFor/openSDK/11/native/sysroot/usr/lib/x86_64-linux-ohos/Scrt1.o D:/harmonyFor/openSDK/11/native/sysroot/usr/lib/x86_64-linux-ohos/crti.o D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/lib/x86_64-linux-ohos/clang_rt.crtbegin.o -LD:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/lib/x86_64-linux-ohos -LD:/harmonyFor/openSDK/11/native/sysroot/usr/lib/ -LD:/harmonyFor/openSDK/11/native/llvm/bin/../lib/x86_64-linux-ohos/ -LD:/harmonyFor/openSDK/11/native/sysroot/usr/lib/x86_64-linux-ohos/ --build-id=sha1 --warn-shared-textrel --fatal-warnings -lunwind --no-undefined -z noexecstack --gc-sections CMakeFiles/cmTC_a2958.dir/CMakeCXXCompilerABI.cpp.o -lc++ -lc++abi -lunwind -lm D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/lib/x86_64-linux-ohos/libclang_rt.builtins.a -l:libunwind.a -lc D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/lib/x86_64-linux-ohos/libclang_rt.builtins.a -l:libunwind.a D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/lib/x86_64-linux-ohos/clang_rt.crtend.o D:/harmonyFor/openSDK/11/native/sysroot/usr/lib/x86_64-linux-ohos/crtn.o




Parsed CXX implicit include dir info from above output: rv=done
  found start of include info
  found start of implicit include info
    add: [D:/harmonyFor/openSDK/11/native/llvm/bin/../include/libcxx-ohos/include/c++/v1]
    add: [D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/include]
    add: [D:/harmonyFor/openSDK/11/native/sysroot/usr/include/x86_64-linux-ohos]
    add: [D:/harmonyFor/openSDK/11/native/sysroot/usr/include]
  end of search list found
  collapse include dir [D:/harmonyFor/openSDK/11/native/llvm/bin/../include/libcxx-ohos/include/c++/v1] ==> [D:/harmonyFor/openSDK/11/native/llvm/include/libcxx-ohos/include/c++/v1]
  collapse include dir [D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/include] ==> [D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/include]
  collapse include dir [D:/harmonyFor/openSDK/11/native/sysroot/usr/include/x86_64-linux-ohos] ==> [D:/harmonyFor/openSDK/11/native/sysroot/usr/include/x86_64-linux-ohos]
  collapse include dir [D:/harmonyFor/openSDK/11/native/sysroot/usr/include] ==> [D:/harmonyFor/openSDK/11/native/sysroot/usr/include]
  implicit include dirs: [D:/harmonyFor/openSDK/11/native/llvm/include/libcxx-ohos/include/c++/v1;D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/include;D:/harmonyFor/openSDK/11/native/sysroot/usr/include/x86_64-linux-ohos;D:/harmonyFor/openSDK/11/native/sysroot/usr/include]


Parsed CXX implicit link information from above output:
  link line regex: [^( *|.*[/\])(ld\.lld\.exe|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\]+-)?ld|collect2)[^/\]*( |$)]
  ignore line: [Change Dir: D:/vp8/ohos_ijkplayer-vp8/ijkplayer/.cxx/default/default/x86_64/CMakeFiles/CMakeTmp]
  ignore line: []
  ignore line: [Run Build Command(s):D:\harmonyFor\openSDK\11\native\build-tools\cmake\bin\ninja.exe cmTC_a2958 && [1/2] Building CXX object CMakeFiles/cmTC_a2958.dir/CMakeCXXCompilerABI.cpp.o]
  ignore line: [OHOS (dev) clang version 15.0.4 (llvm-project 81cdec3cd117b1e6e3a9f1ebc4695d790c978463)]
  ignore line: [Target: x86_64-unknown-linux-ohos]
  ignore line: [Thread model: posix]
  ignore line: [InstalledDir: D:/harmonyFor/openSDK/11/native/llvm/bin]
  ignore line: [clang++: warning: argument unused during compilation: '--gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm' [-Wunused-command-line-argument]]
  ignore line: [clang++: warning: argument unused during compilation: '-s' [-Wunused-command-line-argument]]
  ignore line: [ (in-process)]
  ignore line: [ "D:/harmonyFor/openSDK/11/native/llvm/bin/clang++.exe" -cc1 -triple x86_64-unknown-linux-ohos -emit-obj -mrelax-all --mrelax-relocations -mnoexecstack -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCXXCompilerABI.cpp -mrelocation-model pic -pic-level 2 -pic-is-pie -mframe-pointer=all -ffp-contract=on -fno-rounding-math -mconstructor-aliases -funwind-tables=1 -target-cpu x86-64 -tune-cpu generic -mllvm -treat-scalable-fixed-error-as-warning -debugger-tuning=gdb -v -ffunction-sections -fdata-sections -fcoverage-compilation-dir=D:/vp8/ohos_ijkplayer-vp8/ijkplayer/.cxx/default/default/x86_64/CMakeFiles/CMakeTmp -resource-dir D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4 -D __MUSL__ -isysroot D:/harmonyFor/openSDK/11/native/sysroot -internal-isystem D:/harmonyFor/openSDK/11/native/llvm/bin/../include/libcxx-ohos/include/c++/v1 -internal-isystem D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/include -internal-externc-isystem D:/harmonyFor/openSDK/11/native/sysroot/usr/include/x86_64-linux-ohos -internal-externc-isystem D:/harmonyFor/openSDK/11/native/sysroot/include -internal-externc-isystem D:/harmonyFor/openSDK/11/native/sysroot/usr/include -Wformat -Werror=format-security -fdeprecated-macro -fdebug-compilation-dir=D:/vp8/ohos_ijkplayer-vp8/ijkplayer/.cxx/default/default/x86_64/CMakeFiles/CMakeTmp -ferror-limit 19 -stack-protector 2 -fgnuc-version=4.2.1 -fcxx-exceptions -fexceptions -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_a2958.dir/CMakeCXXCompilerABI.cpp.o -x c++ D:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeCXXCompilerABI.cpp]
  ignore line: [clang -cc1 version 15.0.4 based upon LLVM 15.0.4 default target x86_64-w64-windows-gnu]
  ignore line: [ignoring nonexistent directory "D:/harmonyFor/openSDK/11/native/sysroot/include"]
  ignore line: [#include "..." search starts here:]
  ignore line: [#include <...> search starts here:]
  ignore line: [ D:/harmonyFor/openSDK/11/native/llvm/bin/../include/libcxx-ohos/include/c++/v1]
  ignore line: [ D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/include]
  ignore line: [ D:/harmonyFor/openSDK/11/native/sysroot/usr/include/x86_64-linux-ohos]
  ignore line: [ D:/harmonyFor/openSDK/11/native/sysroot/usr/include]
  ignore line: [End of search list.]
  ignore line: [[2/2] Linking CXX executable cmTC_a2958]
  ignore line: [OHOS (dev) clang version 15.0.4 (llvm-project 81cdec3cd117b1e6e3a9f1ebc4695d790c978463)]
  ignore line: [Target: x86_64-unknown-linux-ohos]
  ignore line: [Thread model: posix]
  ignore line: [InstalledDir: D:/harmonyFor/openSDK/11/native/llvm/bin]
  link line: [ "D:/harmonyFor/openSDK/11/native/llvm/bin/ld.lld" --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -pie -s -z now -z relro -z max-page-size=4096 --hash-style=gnu --hash-style=both --enable-new-dtags --eh-frame-hdr -m elf_x86_64 -dynamic-linker /lib/ld-musl-x86_64.so.1 -o cmTC_a2958 D:/harmonyFor/openSDK/11/native/sysroot/usr/lib/x86_64-linux-ohos/Scrt1.o D:/harmonyFor/openSDK/11/native/sysroot/usr/lib/x86_64-linux-ohos/crti.o D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/lib/x86_64-linux-ohos/clang_rt.crtbegin.o -LD:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/lib/x86_64-linux-ohos -LD:/harmonyFor/openSDK/11/native/sysroot/usr/lib/ -LD:/harmonyFor/openSDK/11/native/llvm/bin/../lib/x86_64-linux-ohos/ -LD:/harmonyFor/openSDK/11/native/sysroot/usr/lib/x86_64-linux-ohos/ --build-id=sha1 --warn-shared-textrel --fatal-warnings -lunwind --no-undefined -z noexecstack --gc-sections CMakeFiles/cmTC_a2958.dir/CMakeCXXCompilerABI.cpp.o -lc++ -lc++abi -lunwind -lm D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/lib/x86_64-linux-ohos/libclang_rt.builtins.a -l:libunwind.a -lc D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/lib/x86_64-linux-ohos/libclang_rt.builtins.a -l:libunwind.a D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/lib/x86_64-linux-ohos/clang_rt.crtend.o D:/harmonyFor/openSDK/11/native/sysroot/usr/lib/x86_64-linux-ohos/crtn.o]
    arg [D:/harmonyFor/openSDK/11/native/llvm/bin/ld.lld] ==> ignore
    arg [--sysroot=D:/harmonyFor/openSDK/11/native/sysroot] ==> ignore
    arg [-pie] ==> ignore
    arg [-s] ==> ignore
    arg [-znow] ==> ignore
    arg [-zrelro] ==> ignore
    arg [-zmax-page-size=4096] ==> ignore
    arg [--hash-style=gnu] ==> ignore
    arg [--hash-style=both] ==> ignore
    arg [--enable-new-dtags] ==> ignore
    arg [--eh-frame-hdr] ==> ignore
    arg [-m] ==> ignore
    arg [elf_x86_64] ==> ignore
    arg [-dynamic-linker] ==> ignore
    arg [/lib/ld-musl-x86_64.so.1] ==> ignore
    arg [-o] ==> ignore
    arg [cmTC_a2958] ==> ignore
    arg [D:/harmonyFor/openSDK/11/native/sysroot/usr/lib/x86_64-linux-ohos/Scrt1.o] ==> ignore
    arg [D:/harmonyFor/openSDK/11/native/sysroot/usr/lib/x86_64-linux-ohos/crti.o] ==> ignore
    arg [D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/lib/x86_64-linux-ohos/clang_rt.crtbegin.o] ==> ignore
    arg [-LD:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/lib/x86_64-linux-ohos] ==> dir [D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/lib/x86_64-linux-ohos]
    arg [-LD:/harmonyFor/openSDK/11/native/sysroot/usr/lib/] ==> dir [D:/harmonyFor/openSDK/11/native/sysroot/usr/lib/]
    arg [-LD:/harmonyFor/openSDK/11/native/llvm/bin/../lib/x86_64-linux-ohos/] ==> dir [D:/harmonyFor/openSDK/11/native/llvm/bin/../lib/x86_64-linux-ohos/]
    arg [-LD:/harmonyFor/openSDK/11/native/sysroot/usr/lib/x86_64-linux-ohos/] ==> dir [D:/harmonyFor/openSDK/11/native/sysroot/usr/lib/x86_64-linux-ohos/]
    arg [--build-id=sha1] ==> ignore
    arg [--warn-shared-textrel] ==> ignore
    arg [--fatal-warnings] ==> ignore
    arg [-lunwind] ==> lib [unwind]
    arg [--no-undefined] ==> ignore
    arg [-znoexecstack] ==> ignore
    arg [--gc-sections] ==> ignore
    arg [CMakeFiles/cmTC_a2958.dir/CMakeCXXCompilerABI.cpp.o] ==> ignore
    arg [-lc++] ==> lib [c++]
    arg [-lc++abi] ==> lib [c++abi]
    arg [-lunwind] ==> lib [unwind]
    arg [-lm] ==> lib [m]
    arg [D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/lib/x86_64-linux-ohos/libclang_rt.builtins.a] ==> lib [D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/lib/x86_64-linux-ohos/libclang_rt.builtins.a]
    arg [-l:libunwind.a] ==> lib [-l:libunwind.a]
    arg [-lc] ==> lib [c]
    arg [D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/lib/x86_64-linux-ohos/libclang_rt.builtins.a] ==> lib [D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/lib/x86_64-linux-ohos/libclang_rt.builtins.a]
    arg [-l:libunwind.a] ==> lib [-l:libunwind.a]
    arg [D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/lib/x86_64-linux-ohos/clang_rt.crtend.o] ==> ignore
    arg [D:/harmonyFor/openSDK/11/native/sysroot/usr/lib/x86_64-linux-ohos/crtn.o] ==> ignore
  remove lib [D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/lib/x86_64-linux-ohos/libclang_rt.builtins.a]
  remove lib [D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/lib/x86_64-linux-ohos/libclang_rt.builtins.a]
  collapse library dir [D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/lib/x86_64-linux-ohos] ==> [D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/lib/x86_64-linux-ohos]
  collapse library dir [D:/harmonyFor/openSDK/11/native/sysroot/usr/lib/] ==> [D:/harmonyFor/openSDK/11/native/sysroot/usr/lib]
  collapse library dir [D:/harmonyFor/openSDK/11/native/llvm/bin/../lib/x86_64-linux-ohos/] ==> [D:/harmonyFor/openSDK/11/native/llvm/lib/x86_64-linux-ohos]
  collapse library dir [D:/harmonyFor/openSDK/11/native/sysroot/usr/lib/x86_64-linux-ohos/] ==> [D:/harmonyFor/openSDK/11/native/sysroot/usr/lib/x86_64-linux-ohos]
  implicit libs: [unwind;c++;c++abi;unwind;m;-l:libunwind.a;c;-l:libunwind.a]
  implicit dirs: [D:/harmonyFor/openSDK/11/native/llvm/lib/clang/15.0.4/lib/x86_64-linux-ohos;D:/harmonyFor/openSDK/11/native/sysroot/usr/lib;D:/harmonyFor/openSDK/11/native/llvm/lib/x86_64-linux-ohos;D:/harmonyFor/openSDK/11/native/sysroot/usr/lib/x86_64-linux-ohos]
  implicit fwks: []


