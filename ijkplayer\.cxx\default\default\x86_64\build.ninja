# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.16

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: ijkplayer
# Configuration: Release
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5

# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include rules.ninja

# =============================================================================
# Object build statements for SHARED_LIBRARY target ijkplayer_napi


#############################################
# Order-only phony target for ijkplayer_napi

build cmake_object_order_depends_target_ijkplayer_napi: phony || cmake_object_order_depends_target_ijkplayer cmake_object_order_depends_target_ijksdl

build CMakeFiles/ijkplayer_napi.dir/napi/ijkplayer_napi_init.cpp.o: CXX_COMPILER__ijkplayer_napi D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/napi/ijkplayer_napi_init.cpp || cmake_object_order_depends_target_ijkplayer_napi
  DEFINES = -DOHOS_PLATFORM -Dijkplayer_napi_EXPORTS
  DEP_FILE = CMakeFiles\ijkplayer_napi.dir\napi\ijkplayer_napi_init.cpp.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security  -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/proxy -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/napi -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/utils/napi -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/third_party/openssl/x86_64/include
  OBJECT_DIR = CMakeFiles\ijkplayer_napi.dir
  OBJECT_FILE_DIR = CMakeFiles\ijkplayer_napi.dir\napi
  TARGET_COMPILE_PDB = CMakeFiles\ijkplayer_napi.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijkplayer_napi.pdb

build CMakeFiles/ijkplayer_napi.dir/napi/ijkplayer_napi.cpp.o: CXX_COMPILER__ijkplayer_napi D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/napi/ijkplayer_napi.cpp || cmake_object_order_depends_target_ijkplayer_napi
  DEFINES = -DOHOS_PLATFORM -Dijkplayer_napi_EXPORTS
  DEP_FILE = CMakeFiles\ijkplayer_napi.dir\napi\ijkplayer_napi.cpp.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security  -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/proxy -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/napi -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/utils/napi -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/third_party/openssl/x86_64/include
  OBJECT_DIR = CMakeFiles\ijkplayer_napi.dir
  OBJECT_FILE_DIR = CMakeFiles\ijkplayer_napi.dir\napi
  TARGET_COMPILE_PDB = CMakeFiles\ijkplayer_napi.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijkplayer_napi.pdb

build CMakeFiles/ijkplayer_napi.dir/napi/ijkplayer_napi_manager.cpp.o: CXX_COMPILER__ijkplayer_napi D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/napi/ijkplayer_napi_manager.cpp || cmake_object_order_depends_target_ijkplayer_napi
  DEFINES = -DOHOS_PLATFORM -Dijkplayer_napi_EXPORTS
  DEP_FILE = CMakeFiles\ijkplayer_napi.dir\napi\ijkplayer_napi_manager.cpp.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security  -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/proxy -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/napi -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/utils/napi -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/third_party/openssl/x86_64/include
  OBJECT_DIR = CMakeFiles\ijkplayer_napi.dir
  OBJECT_FILE_DIR = CMakeFiles\ijkplayer_napi.dir\napi
  TARGET_COMPILE_PDB = CMakeFiles\ijkplayer_napi.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijkplayer_napi.pdb

build CMakeFiles/ijkplayer_napi.dir/proxy/ijkplayer_napi_proxy.cpp.o: CXX_COMPILER__ijkplayer_napi D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/proxy/ijkplayer_napi_proxy.cpp || cmake_object_order_depends_target_ijkplayer_napi
  DEFINES = -DOHOS_PLATFORM -Dijkplayer_napi_EXPORTS
  DEP_FILE = CMakeFiles\ijkplayer_napi.dir\proxy\ijkplayer_napi_proxy.cpp.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security  -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/proxy -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/napi -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/utils/napi -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/third_party/openssl/x86_64/include
  OBJECT_DIR = CMakeFiles\ijkplayer_napi.dir
  OBJECT_FILE_DIR = CMakeFiles\ijkplayer_napi.dir\proxy
  TARGET_COMPILE_PDB = CMakeFiles\ijkplayer_napi.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijkplayer_napi.pdb

build CMakeFiles/ijkplayer_napi.dir/utils/hashmap/data_struct.c.o: C_COMPILER__ijkplayer_napi D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/utils/hashmap/data_struct.c || cmake_object_order_depends_target_ijkplayer_napi
  DEFINES = -DOHOS_PLATFORM -Dijkplayer_napi_EXPORTS
  DEP_FILE = CMakeFiles\ijkplayer_napi.dir\utils\hashmap\data_struct.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/proxy -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/napi -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/utils/napi -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/third_party/openssl/x86_64/include
  OBJECT_DIR = CMakeFiles\ijkplayer_napi.dir
  OBJECT_FILE_DIR = CMakeFiles\ijkplayer_napi.dir\utils\hashmap
  TARGET_COMPILE_PDB = CMakeFiles\ijkplayer_napi.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijkplayer_napi.pdb

build CMakeFiles/ijkplayer_napi.dir/utils/ffmpeg/custom_ffmpeg_log.c.o: C_COMPILER__ijkplayer_napi D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/utils/ffmpeg/custom_ffmpeg_log.c || cmake_object_order_depends_target_ijkplayer_napi
  DEFINES = -DOHOS_PLATFORM -Dijkplayer_napi_EXPORTS
  DEP_FILE = CMakeFiles\ijkplayer_napi.dir\utils\ffmpeg\custom_ffmpeg_log.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/proxy -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/napi -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/utils/napi -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/third_party/openssl/x86_64/include
  OBJECT_DIR = CMakeFiles\ijkplayer_napi.dir
  OBJECT_FILE_DIR = CMakeFiles\ijkplayer_napi.dir\utils\ffmpeg
  TARGET_COMPILE_PDB = CMakeFiles\ijkplayer_napi.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijkplayer_napi.pdb

build CMakeFiles/ijkplayer_napi.dir/utils/napi/napi_utils.cpp.o: CXX_COMPILER__ijkplayer_napi D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/utils/napi/napi_utils.cpp || cmake_object_order_depends_target_ijkplayer_napi
  DEFINES = -DOHOS_PLATFORM -Dijkplayer_napi_EXPORTS
  DEP_FILE = CMakeFiles\ijkplayer_napi.dir\utils\napi\napi_utils.cpp.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security  -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/proxy -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/napi -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/utils/napi -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/third_party/openssl/x86_64/include
  OBJECT_DIR = CMakeFiles\ijkplayer_napi.dir
  OBJECT_FILE_DIR = CMakeFiles\ijkplayer_napi.dir\utils\napi
  TARGET_COMPILE_PDB = CMakeFiles\ijkplayer_napi.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijkplayer_napi.pdb


# =============================================================================
# Link build statements for SHARED_LIBRARY target ijkplayer_napi


#############################################
# Link the shared library D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijkplayer_napi.so

build D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/build/default/intermediates/cmake/default/obj/x86_64/libijkplayer_napi.so: CXX_SHARED_LIBRARY_LINKER__ijkplayer_napi CMakeFiles/ijkplayer_napi.dir/napi/ijkplayer_napi_init.cpp.o CMakeFiles/ijkplayer_napi.dir/napi/ijkplayer_napi.cpp.o CMakeFiles/ijkplayer_napi.dir/napi/ijkplayer_napi_manager.cpp.o CMakeFiles/ijkplayer_napi.dir/proxy/ijkplayer_napi_proxy.cpp.o CMakeFiles/ijkplayer_napi.dir/utils/hashmap/data_struct.c.o CMakeFiles/ijkplayer_napi.dir/utils/ffmpeg/custom_ffmpeg_log.c.o CMakeFiles/ijkplayer_napi.dir/utils/napi/napi_utils.cpp.o | D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/build/default/intermediates/cmake/default/obj/x86_64/libijkplayer.so D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/build/default/intermediates/cmake/default/obj/x86_64/libijksdl.so D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/build/default/intermediates/cmake/default/obj/x86_64/libijksdl.so D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/x86_64/lib/libsoundtouch.a D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/x86_64/lib/libyuv.a || D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/build/default/intermediates/cmake/default/obj/x86_64/libijkplayer.so D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/build/default/intermediates/cmake/default/obj/x86_64/libijksdl.so
  LANGUAGE_COMPILE_FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security  -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -O2 -DNDEBUG
  LINK_FLAGS = --rtlib=compiler-rt -fuse-ld=lld -Wl,--build-id=sha1 -Wl,--warn-shared-textrel -Wl,--fatal-warnings -lunwind -Wl,--no-undefined -Qunused-arguments -Wl,-z,noexecstack
  LINK_LIBRARIES = -Wl,-rpath,D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/x86_64/lib:D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/x86_64/lib:D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/x86_64/lib:D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/x86_64/lib:D:/vp8/ohos_ijkplayer-vp8/ijkplayer/build/default/intermediates/cmake/default/obj/x86_64  D:/vp8/ohos_ijkplayer-vp8/ijkplayer/build/default/intermediates/cmake/default/obj/x86_64/libijkplayer.so  D:/vp8/ohos_ijkplayer-vp8/ijkplayer/build/default/intermediates/cmake/default/obj/x86_64/libijksdl.so  -lEGL  -lGLESv3  -lhilog_ndk.z  -lace_ndk.z  -lace_napi.z  -luv  D:/vp8/ohos_ijkplayer-vp8/ijkplayer/build/default/intermediates/cmake/default/obj/x86_64/libijksdl.so  D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/x86_64/lib/libsoundtouch.a  -lnative_media_codecbase  -lnative_media_core  -lnative_media_vdec  -lnative_window  -lnative_buffer  D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/x86_64/lib/libyuv.a  -lnative_window  -lz  -lavcodec  -lavfilter  -lavformat  -lavutil  -lswresample  -lswscale  -lavdevice  -lcrypto  -lssl  -lohaudio  -lm
  LINK_PATH = -LD:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/x86_64/lib   -LD:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/x86_64/lib   -LD:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/x86_64/lib   -LD:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/x86_64/lib
  OBJECT_DIR = CMakeFiles\ijkplayer_napi.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libijkplayer_napi.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = CMakeFiles\ijkplayer_napi.dir\
  TARGET_FILE = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijkplayer_napi.so
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijkplayer_napi.pdb

# =============================================================================
# Object build statements for SHARED_LIBRARY target ijkplayer_audio_napi


#############################################
# Order-only phony target for ijkplayer_audio_napi

build cmake_object_order_depends_target_ijkplayer_audio_napi: phony || cmake_object_order_depends_target_ijkplayer cmake_object_order_depends_target_ijksdl

build CMakeFiles/ijkplayer_audio_napi.dir/napi/ijkplayer_napi_audio_init.cpp.o: CXX_COMPILER__ijkplayer_audio_napi D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/napi/ijkplayer_napi_audio_init.cpp || cmake_object_order_depends_target_ijkplayer_audio_napi
  DEFINES = -DOHOS_PLATFORM -Dijkplayer_audio_napi_EXPORTS
  DEP_FILE = CMakeFiles\ijkplayer_audio_napi.dir\napi\ijkplayer_napi_audio_init.cpp.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security  -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/proxy -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/napi -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/utils/napi -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/third_party/openssl/x86_64/include
  OBJECT_DIR = CMakeFiles\ijkplayer_audio_napi.dir
  OBJECT_FILE_DIR = CMakeFiles\ijkplayer_audio_napi.dir\napi
  TARGET_COMPILE_PDB = CMakeFiles\ijkplayer_audio_napi.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijkplayer_audio_napi.pdb

build CMakeFiles/ijkplayer_audio_napi.dir/napi/ijkplayer_napi.cpp.o: CXX_COMPILER__ijkplayer_audio_napi D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/napi/ijkplayer_napi.cpp || cmake_object_order_depends_target_ijkplayer_audio_napi
  DEFINES = -DOHOS_PLATFORM -Dijkplayer_audio_napi_EXPORTS
  DEP_FILE = CMakeFiles\ijkplayer_audio_napi.dir\napi\ijkplayer_napi.cpp.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security  -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/proxy -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/napi -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/utils/napi -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/third_party/openssl/x86_64/include
  OBJECT_DIR = CMakeFiles\ijkplayer_audio_napi.dir
  OBJECT_FILE_DIR = CMakeFiles\ijkplayer_audio_napi.dir\napi
  TARGET_COMPILE_PDB = CMakeFiles\ijkplayer_audio_napi.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijkplayer_audio_napi.pdb

build CMakeFiles/ijkplayer_audio_napi.dir/proxy/ijkplayer_napi_proxy.cpp.o: CXX_COMPILER__ijkplayer_audio_napi D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/proxy/ijkplayer_napi_proxy.cpp || cmake_object_order_depends_target_ijkplayer_audio_napi
  DEFINES = -DOHOS_PLATFORM -Dijkplayer_audio_napi_EXPORTS
  DEP_FILE = CMakeFiles\ijkplayer_audio_napi.dir\proxy\ijkplayer_napi_proxy.cpp.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security  -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/proxy -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/napi -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/utils/napi -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/third_party/openssl/x86_64/include
  OBJECT_DIR = CMakeFiles\ijkplayer_audio_napi.dir
  OBJECT_FILE_DIR = CMakeFiles\ijkplayer_audio_napi.dir\proxy
  TARGET_COMPILE_PDB = CMakeFiles\ijkplayer_audio_napi.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijkplayer_audio_napi.pdb

build CMakeFiles/ijkplayer_audio_napi.dir/utils/hashmap/data_struct.c.o: C_COMPILER__ijkplayer_audio_napi D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/utils/hashmap/data_struct.c || cmake_object_order_depends_target_ijkplayer_audio_napi
  DEFINES = -DOHOS_PLATFORM -Dijkplayer_audio_napi_EXPORTS
  DEP_FILE = CMakeFiles\ijkplayer_audio_napi.dir\utils\hashmap\data_struct.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/proxy -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/napi -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/utils/napi -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/third_party/openssl/x86_64/include
  OBJECT_DIR = CMakeFiles\ijkplayer_audio_napi.dir
  OBJECT_FILE_DIR = CMakeFiles\ijkplayer_audio_napi.dir\utils\hashmap
  TARGET_COMPILE_PDB = CMakeFiles\ijkplayer_audio_napi.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijkplayer_audio_napi.pdb

build CMakeFiles/ijkplayer_audio_napi.dir/utils/ffmpeg/custom_ffmpeg_log.c.o: C_COMPILER__ijkplayer_audio_napi D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/utils/ffmpeg/custom_ffmpeg_log.c || cmake_object_order_depends_target_ijkplayer_audio_napi
  DEFINES = -DOHOS_PLATFORM -Dijkplayer_audio_napi_EXPORTS
  DEP_FILE = CMakeFiles\ijkplayer_audio_napi.dir\utils\ffmpeg\custom_ffmpeg_log.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/proxy -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/napi -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/utils/napi -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/third_party/openssl/x86_64/include
  OBJECT_DIR = CMakeFiles\ijkplayer_audio_napi.dir
  OBJECT_FILE_DIR = CMakeFiles\ijkplayer_audio_napi.dir\utils\ffmpeg
  TARGET_COMPILE_PDB = CMakeFiles\ijkplayer_audio_napi.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijkplayer_audio_napi.pdb

build CMakeFiles/ijkplayer_audio_napi.dir/utils/napi/napi_utils.cpp.o: CXX_COMPILER__ijkplayer_audio_napi D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/utils/napi/napi_utils.cpp || cmake_object_order_depends_target_ijkplayer_audio_napi
  DEFINES = -DOHOS_PLATFORM -Dijkplayer_audio_napi_EXPORTS
  DEP_FILE = CMakeFiles\ijkplayer_audio_napi.dir\utils\napi\napi_utils.cpp.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security  -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/proxy -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/napi -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/utils/napi -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/third_party/openssl/x86_64/include
  OBJECT_DIR = CMakeFiles\ijkplayer_audio_napi.dir
  OBJECT_FILE_DIR = CMakeFiles\ijkplayer_audio_napi.dir\utils\napi
  TARGET_COMPILE_PDB = CMakeFiles\ijkplayer_audio_napi.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijkplayer_audio_napi.pdb


# =============================================================================
# Link build statements for SHARED_LIBRARY target ijkplayer_audio_napi


#############################################
# Link the shared library D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijkplayer_audio_napi.so

build D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/build/default/intermediates/cmake/default/obj/x86_64/libijkplayer_audio_napi.so: CXX_SHARED_LIBRARY_LINKER__ijkplayer_audio_napi CMakeFiles/ijkplayer_audio_napi.dir/napi/ijkplayer_napi_audio_init.cpp.o CMakeFiles/ijkplayer_audio_napi.dir/napi/ijkplayer_napi.cpp.o CMakeFiles/ijkplayer_audio_napi.dir/proxy/ijkplayer_napi_proxy.cpp.o CMakeFiles/ijkplayer_audio_napi.dir/utils/hashmap/data_struct.c.o CMakeFiles/ijkplayer_audio_napi.dir/utils/ffmpeg/custom_ffmpeg_log.c.o CMakeFiles/ijkplayer_audio_napi.dir/utils/napi/napi_utils.cpp.o | D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/build/default/intermediates/cmake/default/obj/x86_64/libijkplayer.so D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/build/default/intermediates/cmake/default/obj/x86_64/libijksdl.so D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/build/default/intermediates/cmake/default/obj/x86_64/libijksdl.so D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/x86_64/lib/libsoundtouch.a D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/x86_64/lib/libyuv.a || D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/build/default/intermediates/cmake/default/obj/x86_64/libijkplayer.so D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/build/default/intermediates/cmake/default/obj/x86_64/libijksdl.so
  LANGUAGE_COMPILE_FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security  -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -O2 -DNDEBUG
  LINK_FLAGS = --rtlib=compiler-rt -fuse-ld=lld -Wl,--build-id=sha1 -Wl,--warn-shared-textrel -Wl,--fatal-warnings -lunwind -Wl,--no-undefined -Qunused-arguments -Wl,-z,noexecstack
  LINK_LIBRARIES = -Wl,-rpath,D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/x86_64/lib:D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/x86_64/lib:D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/x86_64/lib:D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/x86_64/lib:D:/vp8/ohos_ijkplayer-vp8/ijkplayer/build/default/intermediates/cmake/default/obj/x86_64  D:/vp8/ohos_ijkplayer-vp8/ijkplayer/build/default/intermediates/cmake/default/obj/x86_64/libijkplayer.so  D:/vp8/ohos_ijkplayer-vp8/ijkplayer/build/default/intermediates/cmake/default/obj/x86_64/libijksdl.so  -lEGL  -lGLESv3  -lhilog_ndk.z  -lace_ndk.z  -lace_napi.z  -luv  D:/vp8/ohos_ijkplayer-vp8/ijkplayer/build/default/intermediates/cmake/default/obj/x86_64/libijksdl.so  D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/x86_64/lib/libsoundtouch.a  -lnative_media_codecbase  -lnative_media_core  -lnative_media_vdec  -lnative_window  -lnative_buffer  D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/x86_64/lib/libyuv.a  -lnative_window  -lz  -lavcodec  -lavfilter  -lavformat  -lavutil  -lswresample  -lswscale  -lavdevice  -lcrypto  -lssl  -lohaudio  -lm
  LINK_PATH = -LD:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/x86_64/lib   -LD:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/x86_64/lib   -LD:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/x86_64/lib   -LD:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/x86_64/lib
  OBJECT_DIR = CMakeFiles\ijkplayer_audio_napi.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libijkplayer_audio_napi.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = CMakeFiles\ijkplayer_audio_napi.dir\
  TARGET_FILE = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijkplayer_audio_napi.so
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijkplayer_audio_napi.pdb


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\vp8\ohos_ijkplayer-vp8\ijkplayer\.cxx\default\default\x86_64 && D:\harmonyFor\openSDK\11\native\build-tools\cmake\bin\cmake-gui.exe -SD:\vp8\ohos_ijkplayer-vp8\ijkplayer\src\main\cpp -BD:\vp8\ohos_ijkplayer-vp8\ijkplayer\.cxx\default\default\x86_64"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\vp8\ohos_ijkplayer-vp8\ijkplayer\.cxx\default\default\x86_64 && D:\harmonyFor\openSDK\11\native\build-tools\cmake\bin\cmake.exe -SD:\vp8\ohos_ijkplayer-vp8\ijkplayer\src\main\cpp -BD:\vp8\ohos_ijkplayer-vp8\ijkplayer\.cxx\default\default\x86_64"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build ijksdl/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\vp8\ohos_ijkplayer-vp8\ijkplayer\.cxx\default\default\x86_64\ijksdl && D:\harmonyFor\openSDK\11\native\build-tools\cmake\bin\cmake-gui.exe -SD:\vp8\ohos_ijkplayer-vp8\ijkplayer\src\main\cpp -BD:\vp8\ohos_ijkplayer-vp8\ijkplayer\.cxx\default\default\x86_64"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build ijksdl/edit_cache: phony ijksdl/CMakeFiles/edit_cache.util

# =============================================================================
# Object build statements for SHARED_LIBRARY target ijksdl


#############################################
# Order-only phony target for ijksdl

build cmake_object_order_depends_target_ijksdl: phony || ijksdl/CMakeFiles/ijksdl.dir

build ijksdl/CMakeFiles/ijksdl.dir/ijksdl_aout.c.o: C_COMPILER__ijksdl D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/ijksdl_aout.c || cmake_object_order_depends_target_ijksdl
  DEFINES = -DOHOS_PLATFORM -Dijksdl_EXPORTS
  DEP_FILE = ijksdl\CMakeFiles\ijksdl.dir\ijksdl_aout.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijksdl\CMakeFiles\ijksdl.dir
  OBJECT_FILE_DIR = ijksdl\CMakeFiles\ijksdl.dir
  TARGET_COMPILE_PDB = ijksdl\CMakeFiles\ijksdl.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijksdl.pdb

build ijksdl/CMakeFiles/ijksdl.dir/ijksdl_audio.c.o: C_COMPILER__ijksdl D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/ijksdl_audio.c || cmake_object_order_depends_target_ijksdl
  DEFINES = -DOHOS_PLATFORM -Dijksdl_EXPORTS
  DEP_FILE = ijksdl\CMakeFiles\ijksdl.dir\ijksdl_audio.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijksdl\CMakeFiles\ijksdl.dir
  OBJECT_FILE_DIR = ijksdl\CMakeFiles\ijksdl.dir
  TARGET_COMPILE_PDB = ijksdl\CMakeFiles\ijksdl.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijksdl.pdb

build ijksdl/CMakeFiles/ijksdl.dir/ijksdl_egl.c.o: C_COMPILER__ijksdl D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/ijksdl_egl.c || cmake_object_order_depends_target_ijksdl
  DEFINES = -DOHOS_PLATFORM -Dijksdl_EXPORTS
  DEP_FILE = ijksdl\CMakeFiles\ijksdl.dir\ijksdl_egl.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijksdl\CMakeFiles\ijksdl.dir
  OBJECT_FILE_DIR = ijksdl\CMakeFiles\ijksdl.dir
  TARGET_COMPILE_PDB = ijksdl\CMakeFiles\ijksdl.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijksdl.pdb

build ijksdl/CMakeFiles/ijksdl.dir/ijksdl_error.c.o: C_COMPILER__ijksdl D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/ijksdl_error.c || cmake_object_order_depends_target_ijksdl
  DEFINES = -DOHOS_PLATFORM -Dijksdl_EXPORTS
  DEP_FILE = ijksdl\CMakeFiles\ijksdl.dir\ijksdl_error.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijksdl\CMakeFiles\ijksdl.dir
  OBJECT_FILE_DIR = ijksdl\CMakeFiles\ijksdl.dir
  TARGET_COMPILE_PDB = ijksdl\CMakeFiles\ijksdl.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijksdl.pdb

build ijksdl/CMakeFiles/ijksdl.dir/ijksdl_mutex.c.o: C_COMPILER__ijksdl D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/ijksdl_mutex.c || cmake_object_order_depends_target_ijksdl
  DEFINES = -DOHOS_PLATFORM -Dijksdl_EXPORTS
  DEP_FILE = ijksdl\CMakeFiles\ijksdl.dir\ijksdl_mutex.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijksdl\CMakeFiles\ijksdl.dir
  OBJECT_FILE_DIR = ijksdl\CMakeFiles\ijksdl.dir
  TARGET_COMPILE_PDB = ijksdl\CMakeFiles\ijksdl.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijksdl.pdb

build ijksdl/CMakeFiles/ijksdl.dir/ijksdl_stdinc.c.o: C_COMPILER__ijksdl D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/ijksdl_stdinc.c || cmake_object_order_depends_target_ijksdl
  DEFINES = -DOHOS_PLATFORM -Dijksdl_EXPORTS
  DEP_FILE = ijksdl\CMakeFiles\ijksdl.dir\ijksdl_stdinc.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijksdl\CMakeFiles\ijksdl.dir
  OBJECT_FILE_DIR = ijksdl\CMakeFiles\ijksdl.dir
  TARGET_COMPILE_PDB = ijksdl\CMakeFiles\ijksdl.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijksdl.pdb

build ijksdl/CMakeFiles/ijksdl.dir/ijksdl_thread.c.o: C_COMPILER__ijksdl D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/ijksdl_thread.c || cmake_object_order_depends_target_ijksdl
  DEFINES = -DOHOS_PLATFORM -Dijksdl_EXPORTS
  DEP_FILE = ijksdl\CMakeFiles\ijksdl.dir\ijksdl_thread.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijksdl\CMakeFiles\ijksdl.dir
  OBJECT_FILE_DIR = ijksdl\CMakeFiles\ijksdl.dir
  TARGET_COMPILE_PDB = ijksdl\CMakeFiles\ijksdl.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijksdl.pdb

build ijksdl/CMakeFiles/ijksdl.dir/ijksdl_timer.c.o: C_COMPILER__ijksdl D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/ijksdl_timer.c || cmake_object_order_depends_target_ijksdl
  DEFINES = -DOHOS_PLATFORM -Dijksdl_EXPORTS
  DEP_FILE = ijksdl\CMakeFiles\ijksdl.dir\ijksdl_timer.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijksdl\CMakeFiles\ijksdl.dir
  OBJECT_FILE_DIR = ijksdl\CMakeFiles\ijksdl.dir
  TARGET_COMPILE_PDB = ijksdl\CMakeFiles\ijksdl.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijksdl.pdb

build ijksdl/CMakeFiles/ijksdl.dir/ijksdl_vout.c.o: C_COMPILER__ijksdl D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/ijksdl_vout.c || cmake_object_order_depends_target_ijksdl
  DEFINES = -DOHOS_PLATFORM -Dijksdl_EXPORTS
  DEP_FILE = ijksdl\CMakeFiles\ijksdl.dir\ijksdl_vout.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijksdl\CMakeFiles\ijksdl.dir
  OBJECT_FILE_DIR = ijksdl\CMakeFiles\ijksdl.dir
  TARGET_COMPILE_PDB = ijksdl\CMakeFiles\ijksdl.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijksdl.pdb

build ijksdl/CMakeFiles/ijksdl.dir/ijksdl_extra_log.c.o: C_COMPILER__ijksdl D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/ijksdl_extra_log.c || cmake_object_order_depends_target_ijksdl
  DEFINES = -DOHOS_PLATFORM -Dijksdl_EXPORTS
  DEP_FILE = ijksdl\CMakeFiles\ijksdl.dir\ijksdl_extra_log.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijksdl\CMakeFiles\ijksdl.dir
  OBJECT_FILE_DIR = ijksdl\CMakeFiles\ijksdl.dir
  TARGET_COMPILE_PDB = ijksdl\CMakeFiles\ijksdl.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijksdl.pdb

build ijksdl/CMakeFiles/ijksdl.dir/video/gles2/color.c.o: C_COMPILER__ijksdl D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/video/gles2/color.c || cmake_object_order_depends_target_ijksdl
  DEFINES = -DOHOS_PLATFORM -Dijksdl_EXPORTS
  DEP_FILE = ijksdl\CMakeFiles\ijksdl.dir\video\gles2\color.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijksdl\CMakeFiles\ijksdl.dir
  OBJECT_FILE_DIR = ijksdl\CMakeFiles\ijksdl.dir\video\gles2
  TARGET_COMPILE_PDB = ijksdl\CMakeFiles\ijksdl.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijksdl.pdb

build ijksdl/CMakeFiles/ijksdl.dir/video/gles2/common.c.o: C_COMPILER__ijksdl D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/video/gles2/common.c || cmake_object_order_depends_target_ijksdl
  DEFINES = -DOHOS_PLATFORM -Dijksdl_EXPORTS
  DEP_FILE = ijksdl\CMakeFiles\ijksdl.dir\video\gles2\common.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijksdl\CMakeFiles\ijksdl.dir
  OBJECT_FILE_DIR = ijksdl\CMakeFiles\ijksdl.dir\video\gles2
  TARGET_COMPILE_PDB = ijksdl\CMakeFiles\ijksdl.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijksdl.pdb

build ijksdl/CMakeFiles/ijksdl.dir/video/gles2/renderer.c.o: C_COMPILER__ijksdl D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/video/gles2/renderer.c || cmake_object_order_depends_target_ijksdl
  DEFINES = -DOHOS_PLATFORM -Dijksdl_EXPORTS
  DEP_FILE = ijksdl\CMakeFiles\ijksdl.dir\video\gles2\renderer.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijksdl\CMakeFiles\ijksdl.dir
  OBJECT_FILE_DIR = ijksdl\CMakeFiles\ijksdl.dir\video\gles2
  TARGET_COMPILE_PDB = ijksdl\CMakeFiles\ijksdl.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijksdl.pdb

build ijksdl/CMakeFiles/ijksdl.dir/video/gles2/renderer_rgb.c.o: C_COMPILER__ijksdl D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/video/gles2/renderer_rgb.c || cmake_object_order_depends_target_ijksdl
  DEFINES = -DOHOS_PLATFORM -Dijksdl_EXPORTS
  DEP_FILE = ijksdl\CMakeFiles\ijksdl.dir\video\gles2\renderer_rgb.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijksdl\CMakeFiles\ijksdl.dir
  OBJECT_FILE_DIR = ijksdl\CMakeFiles\ijksdl.dir\video\gles2
  TARGET_COMPILE_PDB = ijksdl\CMakeFiles\ijksdl.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijksdl.pdb

build ijksdl/CMakeFiles/ijksdl.dir/video/gles2/renderer_yuv420p.c.o: C_COMPILER__ijksdl D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/video/gles2/renderer_yuv420p.c || cmake_object_order_depends_target_ijksdl
  DEFINES = -DOHOS_PLATFORM -Dijksdl_EXPORTS
  DEP_FILE = ijksdl\CMakeFiles\ijksdl.dir\video\gles2\renderer_yuv420p.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijksdl\CMakeFiles\ijksdl.dir
  OBJECT_FILE_DIR = ijksdl\CMakeFiles\ijksdl.dir\video\gles2
  TARGET_COMPILE_PDB = ijksdl\CMakeFiles\ijksdl.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijksdl.pdb

build ijksdl/CMakeFiles/ijksdl.dir/video/gles2/renderer_yuv444p10le.c.o: C_COMPILER__ijksdl D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/video/gles2/renderer_yuv444p10le.c || cmake_object_order_depends_target_ijksdl
  DEFINES = -DOHOS_PLATFORM -Dijksdl_EXPORTS
  DEP_FILE = ijksdl\CMakeFiles\ijksdl.dir\video\gles2\renderer_yuv444p10le.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijksdl\CMakeFiles\ijksdl.dir
  OBJECT_FILE_DIR = ijksdl\CMakeFiles\ijksdl.dir\video\gles2
  TARGET_COMPILE_PDB = ijksdl\CMakeFiles\ijksdl.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijksdl.pdb

build ijksdl/CMakeFiles/ijksdl.dir/video/gles2/shader.c.o: C_COMPILER__ijksdl D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/video/gles2/shader.c || cmake_object_order_depends_target_ijksdl
  DEFINES = -DOHOS_PLATFORM -Dijksdl_EXPORTS
  DEP_FILE = ijksdl\CMakeFiles\ijksdl.dir\video\gles2\shader.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijksdl\CMakeFiles\ijksdl.dir
  OBJECT_FILE_DIR = ijksdl\CMakeFiles\ijksdl.dir\video\gles2
  TARGET_COMPILE_PDB = ijksdl\CMakeFiles\ijksdl.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijksdl.pdb

build ijksdl/CMakeFiles/ijksdl.dir/video/gles2/fsh/rgb.fsh.c.o: C_COMPILER__ijksdl D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/video/gles2/fsh/rgb.fsh.c || cmake_object_order_depends_target_ijksdl
  DEFINES = -DOHOS_PLATFORM -Dijksdl_EXPORTS
  DEP_FILE = ijksdl\CMakeFiles\ijksdl.dir\video\gles2\fsh\rgb.fsh.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijksdl\CMakeFiles\ijksdl.dir
  OBJECT_FILE_DIR = ijksdl\CMakeFiles\ijksdl.dir\video\gles2\fsh
  TARGET_COMPILE_PDB = ijksdl\CMakeFiles\ijksdl.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijksdl.pdb

build ijksdl/CMakeFiles/ijksdl.dir/video/gles2/fsh/yuv420p.fsh.c.o: C_COMPILER__ijksdl D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/video/gles2/fsh/yuv420p.fsh.c || cmake_object_order_depends_target_ijksdl
  DEFINES = -DOHOS_PLATFORM -Dijksdl_EXPORTS
  DEP_FILE = ijksdl\CMakeFiles\ijksdl.dir\video\gles2\fsh\yuv420p.fsh.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijksdl\CMakeFiles\ijksdl.dir
  OBJECT_FILE_DIR = ijksdl\CMakeFiles\ijksdl.dir\video\gles2\fsh
  TARGET_COMPILE_PDB = ijksdl\CMakeFiles\ijksdl.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijksdl.pdb

build ijksdl/CMakeFiles/ijksdl.dir/video/gles2/fsh/yuv444p10le.fsh.c.o: C_COMPILER__ijksdl D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/video/gles2/fsh/yuv444p10le.fsh.c || cmake_object_order_depends_target_ijksdl
  DEFINES = -DOHOS_PLATFORM -Dijksdl_EXPORTS
  DEP_FILE = ijksdl\CMakeFiles\ijksdl.dir\video\gles2\fsh\yuv444p10le.fsh.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijksdl\CMakeFiles\ijksdl.dir
  OBJECT_FILE_DIR = ijksdl\CMakeFiles\ijksdl.dir\video\gles2\fsh
  TARGET_COMPILE_PDB = ijksdl\CMakeFiles\ijksdl.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijksdl.pdb

build ijksdl/CMakeFiles/ijksdl.dir/video/gles2/vsh/mvp.vsh.c.o: C_COMPILER__ijksdl D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/video/gles2/vsh/mvp.vsh.c || cmake_object_order_depends_target_ijksdl
  DEFINES = -DOHOS_PLATFORM -Dijksdl_EXPORTS
  DEP_FILE = ijksdl\CMakeFiles\ijksdl.dir\video\gles2\vsh\mvp.vsh.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijksdl\CMakeFiles\ijksdl.dir
  OBJECT_FILE_DIR = ijksdl\CMakeFiles\ijksdl.dir\video\gles2\vsh
  TARGET_COMPILE_PDB = ijksdl\CMakeFiles\ijksdl.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijksdl.pdb

build ijksdl/CMakeFiles/ijksdl.dir/video/ijksdl_vout_android_nativewindow.c.o: C_COMPILER__ijksdl D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/video/ijksdl_vout_android_nativewindow.c || cmake_object_order_depends_target_ijksdl
  DEFINES = -DOHOS_PLATFORM -Dijksdl_EXPORTS
  DEP_FILE = ijksdl\CMakeFiles\ijksdl.dir\video\ijksdl_vout_android_nativewindow.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijksdl\CMakeFiles\ijksdl.dir
  OBJECT_FILE_DIR = ijksdl\CMakeFiles\ijksdl.dir\video
  TARGET_COMPILE_PDB = ijksdl\CMakeFiles\ijksdl.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijksdl.pdb

build ijksdl/CMakeFiles/ijksdl.dir/video/ijksdl_vout_android_surface.c.o: C_COMPILER__ijksdl D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/video/ijksdl_vout_android_surface.c || cmake_object_order_depends_target_ijksdl
  DEFINES = -DOHOS_PLATFORM -Dijksdl_EXPORTS
  DEP_FILE = ijksdl\CMakeFiles\ijksdl.dir\video\ijksdl_vout_android_surface.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijksdl\CMakeFiles\ijksdl.dir
  OBJECT_FILE_DIR = ijksdl\CMakeFiles\ijksdl.dir\video
  TARGET_COMPILE_PDB = ijksdl\CMakeFiles\ijksdl.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijksdl.pdb

build ijksdl/CMakeFiles/ijksdl.dir/ffmpeg/ijksdl_vout_overlay_ffmpeg.c.o: C_COMPILER__ijksdl D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/ffmpeg/ijksdl_vout_overlay_ffmpeg.c || cmake_object_order_depends_target_ijksdl
  DEFINES = -DOHOS_PLATFORM -Dijksdl_EXPORTS
  DEP_FILE = ijksdl\CMakeFiles\ijksdl.dir\ffmpeg\ijksdl_vout_overlay_ffmpeg.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijksdl\CMakeFiles\ijksdl.dir
  OBJECT_FILE_DIR = ijksdl\CMakeFiles\ijksdl.dir\ffmpeg
  TARGET_COMPILE_PDB = ijksdl\CMakeFiles\ijksdl.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijksdl.pdb

build ijksdl/CMakeFiles/ijksdl.dir/ffmpeg/abi_all/image_convert.c.o: C_COMPILER__ijksdl D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/ffmpeg/abi_all/image_convert.c || cmake_object_order_depends_target_ijksdl
  DEFINES = -DOHOS_PLATFORM -Dijksdl_EXPORTS
  DEP_FILE = ijksdl\CMakeFiles\ijksdl.dir\ffmpeg\abi_all\image_convert.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijksdl\CMakeFiles\ijksdl.dir
  OBJECT_FILE_DIR = ijksdl\CMakeFiles\ijksdl.dir\ffmpeg\abi_all
  TARGET_COMPILE_PDB = ijksdl\CMakeFiles\ijksdl.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijksdl.pdb

build ijksdl/CMakeFiles/ijksdl.dir/audio/ijksdl_aout_android_opensles.c.o: C_COMPILER__ijksdl D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/audio/ijksdl_aout_android_opensles.c || cmake_object_order_depends_target_ijksdl
  DEFINES = -DOHOS_PLATFORM -Dijksdl_EXPORTS
  DEP_FILE = ijksdl\CMakeFiles\ijksdl.dir\audio\ijksdl_aout_android_opensles.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijksdl\CMakeFiles\ijksdl.dir
  OBJECT_FILE_DIR = ijksdl\CMakeFiles\ijksdl.dir\audio
  TARGET_COMPILE_PDB = ijksdl\CMakeFiles\ijksdl.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijksdl.pdb

build ijksdl/CMakeFiles/ijksdl.dir/dummy/ijksdl_vout_dummy.c.o: C_COMPILER__ijksdl D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/dummy/ijksdl_vout_dummy.c || cmake_object_order_depends_target_ijksdl
  DEFINES = -DOHOS_PLATFORM -Dijksdl_EXPORTS
  DEP_FILE = ijksdl\CMakeFiles\ijksdl.dir\dummy\ijksdl_vout_dummy.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijksdl\CMakeFiles\ijksdl.dir
  OBJECT_FILE_DIR = ijksdl\CMakeFiles\ijksdl.dir\dummy
  TARGET_COMPILE_PDB = ijksdl\CMakeFiles\ijksdl.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijksdl.pdb

build ijksdl/CMakeFiles/ijksdl.dir/__/utils/ohoslog/ohos_log.cpp.o: CXX_COMPILER__ijksdl D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/utils/ohoslog/ohos_log.cpp || cmake_object_order_depends_target_ijksdl
  DEFINES = -DOHOS_PLATFORM -Dijksdl_EXPORTS
  DEP_FILE = ijksdl\CMakeFiles\ijksdl.dir\__\utils\ohoslog\ohos_log.cpp.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security  -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijksdl\CMakeFiles\ijksdl.dir
  OBJECT_FILE_DIR = ijksdl\CMakeFiles\ijksdl.dir\__\utils\ohoslog
  TARGET_COMPILE_PDB = ijksdl\CMakeFiles\ijksdl.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijksdl.pdb


# =============================================================================
# Link build statements for SHARED_LIBRARY target ijksdl


#############################################
# Link the shared library D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijksdl.so

build D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/build/default/intermediates/cmake/default/obj/x86_64/libijksdl.so: CXX_SHARED_LIBRARY_LINKER__ijksdl ijksdl/CMakeFiles/ijksdl.dir/ijksdl_aout.c.o ijksdl/CMakeFiles/ijksdl.dir/ijksdl_audio.c.o ijksdl/CMakeFiles/ijksdl.dir/ijksdl_egl.c.o ijksdl/CMakeFiles/ijksdl.dir/ijksdl_error.c.o ijksdl/CMakeFiles/ijksdl.dir/ijksdl_mutex.c.o ijksdl/CMakeFiles/ijksdl.dir/ijksdl_stdinc.c.o ijksdl/CMakeFiles/ijksdl.dir/ijksdl_thread.c.o ijksdl/CMakeFiles/ijksdl.dir/ijksdl_timer.c.o ijksdl/CMakeFiles/ijksdl.dir/ijksdl_vout.c.o ijksdl/CMakeFiles/ijksdl.dir/ijksdl_extra_log.c.o ijksdl/CMakeFiles/ijksdl.dir/video/gles2/color.c.o ijksdl/CMakeFiles/ijksdl.dir/video/gles2/common.c.o ijksdl/CMakeFiles/ijksdl.dir/video/gles2/renderer.c.o ijksdl/CMakeFiles/ijksdl.dir/video/gles2/renderer_rgb.c.o ijksdl/CMakeFiles/ijksdl.dir/video/gles2/renderer_yuv420p.c.o ijksdl/CMakeFiles/ijksdl.dir/video/gles2/renderer_yuv444p10le.c.o ijksdl/CMakeFiles/ijksdl.dir/video/gles2/shader.c.o ijksdl/CMakeFiles/ijksdl.dir/video/gles2/fsh/rgb.fsh.c.o ijksdl/CMakeFiles/ijksdl.dir/video/gles2/fsh/yuv420p.fsh.c.o ijksdl/CMakeFiles/ijksdl.dir/video/gles2/fsh/yuv444p10le.fsh.c.o ijksdl/CMakeFiles/ijksdl.dir/video/gles2/vsh/mvp.vsh.c.o ijksdl/CMakeFiles/ijksdl.dir/video/ijksdl_vout_android_nativewindow.c.o ijksdl/CMakeFiles/ijksdl.dir/video/ijksdl_vout_android_surface.c.o ijksdl/CMakeFiles/ijksdl.dir/ffmpeg/ijksdl_vout_overlay_ffmpeg.c.o ijksdl/CMakeFiles/ijksdl.dir/ffmpeg/abi_all/image_convert.c.o ijksdl/CMakeFiles/ijksdl.dir/audio/ijksdl_aout_android_opensles.c.o ijksdl/CMakeFiles/ijksdl.dir/dummy/ijksdl_vout_dummy.c.o ijksdl/CMakeFiles/ijksdl.dir/__/utils/ohoslog/ohos_log.cpp.o | D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/x86_64/lib/libyuv.a
  LANGUAGE_COMPILE_FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security  -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -O2 -DNDEBUG
  LINK_FLAGS = --rtlib=compiler-rt -fuse-ld=lld -Wl,--build-id=sha1 -Wl,--warn-shared-textrel -Wl,--fatal-warnings -lunwind -Wl,--no-undefined -Qunused-arguments -Wl,-z,noexecstack
  LINK_LIBRARIES = -Wl,-rpath,D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/x86_64/lib:D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/x86_64/lib  D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/yuv/x86_64/lib/libyuv.a  -lEGL  -lGLESv3  -lhilog_ndk.z  -lnative_window  -lz  -lavcodec  -lavfilter  -lavformat  -lavutil  -lswresample  -lswscale  -lavdevice  -lcrypto  -lssl  -lohaudio  -lm
  LINK_PATH = -LD:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/ffmpeg/ffmpeg/x86_64/lib   -LD:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/../third_party/openssl/x86_64/lib
  OBJECT_DIR = ijksdl\CMakeFiles\ijksdl.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libijksdl.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = ijksdl\CMakeFiles\ijksdl.dir\
  TARGET_FILE = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijksdl.so
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijksdl.pdb


#############################################
# Utility command for rebuild_cache

build ijksdl/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\vp8\ohos_ijkplayer-vp8\ijkplayer\.cxx\default\default\x86_64\ijksdl && D:\harmonyFor\openSDK\11\native\build-tools\cmake\bin\cmake.exe -SD:\vp8\ohos_ijkplayer-vp8\ijkplayer\src\main\cpp -BD:\vp8\ohos_ijkplayer-vp8\ijkplayer\.cxx\default\default\x86_64"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build ijksdl/rebuild_cache: phony ijksdl/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target ijkplayer


#############################################
# Order-only phony target for ijkplayer

build cmake_object_order_depends_target_ijkplayer: phony || ijkplayer/CMakeFiles/ijkplayer.dir

build ijkplayer/CMakeFiles/ijkplayer.dir/ff_cmdutils.c.o: C_COMPILER__ijkplayer D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/ff_cmdutils.c || cmake_object_order_depends_target_ijkplayer
  DEFINES = -DOHOS_PLATFORM -Dijkplayer_EXPORTS
  DEP_FILE = ijkplayer\CMakeFiles\ijkplayer.dir\ff_cmdutils.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijkplayer\CMakeFiles\ijkplayer.dir
  OBJECT_FILE_DIR = ijkplayer\CMakeFiles\ijkplayer.dir
  TARGET_COMPILE_PDB = ijkplayer\CMakeFiles\ijkplayer.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijkplayer.pdb

build ijkplayer/CMakeFiles/ijkplayer.dir/ff_ffplay.c.o: C_COMPILER__ijkplayer D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/ff_ffplay.c || cmake_object_order_depends_target_ijkplayer
  DEFINES = -DOHOS_PLATFORM -Dijkplayer_EXPORTS
  DEP_FILE = ijkplayer\CMakeFiles\ijkplayer.dir\ff_ffplay.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijkplayer\CMakeFiles\ijkplayer.dir
  OBJECT_FILE_DIR = ijkplayer\CMakeFiles\ijkplayer.dir
  TARGET_COMPILE_PDB = ijkplayer\CMakeFiles\ijkplayer.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijkplayer.pdb

build ijkplayer/CMakeFiles/ijkplayer.dir/ff_ffpipeline.c.o: C_COMPILER__ijkplayer D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/ff_ffpipeline.c || cmake_object_order_depends_target_ijkplayer
  DEFINES = -DOHOS_PLATFORM -Dijkplayer_EXPORTS
  DEP_FILE = ijkplayer\CMakeFiles\ijkplayer.dir\ff_ffpipeline.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijkplayer\CMakeFiles\ijkplayer.dir
  OBJECT_FILE_DIR = ijkplayer\CMakeFiles\ijkplayer.dir
  TARGET_COMPILE_PDB = ijkplayer\CMakeFiles\ijkplayer.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijkplayer.pdb

build ijkplayer/CMakeFiles/ijkplayer.dir/ff_ffpipenode.c.o: C_COMPILER__ijkplayer D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/ff_ffpipenode.c || cmake_object_order_depends_target_ijkplayer
  DEFINES = -DOHOS_PLATFORM -Dijkplayer_EXPORTS
  DEP_FILE = ijkplayer\CMakeFiles\ijkplayer.dir\ff_ffpipenode.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijkplayer\CMakeFiles\ijkplayer.dir
  OBJECT_FILE_DIR = ijkplayer\CMakeFiles\ijkplayer.dir
  TARGET_COMPILE_PDB = ijkplayer\CMakeFiles\ijkplayer.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijkplayer.pdb

build ijkplayer/CMakeFiles/ijkplayer.dir/ijkmeta.c.o: C_COMPILER__ijkplayer D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/ijkmeta.c || cmake_object_order_depends_target_ijkplayer
  DEFINES = -DOHOS_PLATFORM -Dijkplayer_EXPORTS
  DEP_FILE = ijkplayer\CMakeFiles\ijkplayer.dir\ijkmeta.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijkplayer\CMakeFiles\ijkplayer.dir
  OBJECT_FILE_DIR = ijkplayer\CMakeFiles\ijkplayer.dir
  TARGET_COMPILE_PDB = ijkplayer\CMakeFiles\ijkplayer.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijkplayer.pdb

build ijkplayer/CMakeFiles/ijkplayer.dir/ijkplayer.c.o: C_COMPILER__ijkplayer D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/ijkplayer.c || cmake_object_order_depends_target_ijkplayer
  DEFINES = -DOHOS_PLATFORM -Dijkplayer_EXPORTS
  DEP_FILE = ijkplayer\CMakeFiles\ijkplayer.dir\ijkplayer.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijkplayer\CMakeFiles\ijkplayer.dir
  OBJECT_FILE_DIR = ijkplayer\CMakeFiles\ijkplayer.dir
  TARGET_COMPILE_PDB = ijkplayer\CMakeFiles\ijkplayer.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijkplayer.pdb

build ijkplayer/CMakeFiles/ijkplayer.dir/ijkplayer_android.c.o: C_COMPILER__ijkplayer D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/ijkplayer_android.c || cmake_object_order_depends_target_ijkplayer
  DEFINES = -DOHOS_PLATFORM -Dijkplayer_EXPORTS
  DEP_FILE = ijkplayer\CMakeFiles\ijkplayer.dir\ijkplayer_android.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijkplayer\CMakeFiles\ijkplayer.dir
  OBJECT_FILE_DIR = ijkplayer\CMakeFiles\ijkplayer.dir
  TARGET_COMPILE_PDB = ijkplayer\CMakeFiles\ijkplayer.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijkplayer.pdb

build ijkplayer/CMakeFiles/ijkplayer.dir/pipeline/ffpipenode_ffplay_vdec.c.o: C_COMPILER__ijkplayer D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/pipeline/ffpipenode_ffplay_vdec.c || cmake_object_order_depends_target_ijkplayer
  DEFINES = -DOHOS_PLATFORM -Dijkplayer_EXPORTS
  DEP_FILE = ijkplayer\CMakeFiles\ijkplayer.dir\pipeline\ffpipenode_ffplay_vdec.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijkplayer\CMakeFiles\ijkplayer.dir
  OBJECT_FILE_DIR = ijkplayer\CMakeFiles\ijkplayer.dir\pipeline
  TARGET_COMPILE_PDB = ijkplayer\CMakeFiles\ijkplayer.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijkplayer.pdb

build ijkplayer/CMakeFiles/ijkplayer.dir/pipeline/ffpipeline_android.c.o: C_COMPILER__ijkplayer D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/pipeline/ffpipeline_android.c || cmake_object_order_depends_target_ijkplayer
  DEFINES = -DOHOS_PLATFORM -Dijkplayer_EXPORTS
  DEP_FILE = ijkplayer\CMakeFiles\ijkplayer.dir\pipeline\ffpipeline_android.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijkplayer\CMakeFiles\ijkplayer.dir
  OBJECT_FILE_DIR = ijkplayer\CMakeFiles\ijkplayer.dir\pipeline
  TARGET_COMPILE_PDB = ijkplayer\CMakeFiles\ijkplayer.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijkplayer.pdb

build ijkplayer/CMakeFiles/ijkplayer.dir/ijkavformat/allformats.c.o: C_COMPILER__ijkplayer D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/ijkavformat/allformats.c || cmake_object_order_depends_target_ijkplayer
  DEFINES = -DOHOS_PLATFORM -Dijkplayer_EXPORTS
  DEP_FILE = ijkplayer\CMakeFiles\ijkplayer.dir\ijkavformat\allformats.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijkplayer\CMakeFiles\ijkplayer.dir
  OBJECT_FILE_DIR = ijkplayer\CMakeFiles\ijkplayer.dir\ijkavformat
  TARGET_COMPILE_PDB = ijkplayer\CMakeFiles\ijkplayer.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijkplayer.pdb

build ijkplayer/CMakeFiles/ijkplayer.dir/ijkavformat/ijklivehook.c.o: C_COMPILER__ijkplayer D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/ijkavformat/ijklivehook.c || cmake_object_order_depends_target_ijkplayer
  DEFINES = -DOHOS_PLATFORM -Dijkplayer_EXPORTS
  DEP_FILE = ijkplayer\CMakeFiles\ijkplayer.dir\ijkavformat\ijklivehook.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijkplayer\CMakeFiles\ijkplayer.dir
  OBJECT_FILE_DIR = ijkplayer\CMakeFiles\ijkplayer.dir\ijkavformat
  TARGET_COMPILE_PDB = ijkplayer\CMakeFiles\ijkplayer.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijkplayer.pdb

build ijkplayer/CMakeFiles/ijkplayer.dir/ijkavformat/ijkio.c.o: C_COMPILER__ijkplayer D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/ijkavformat/ijkio.c || cmake_object_order_depends_target_ijkplayer
  DEFINES = -DOHOS_PLATFORM -Dijkplayer_EXPORTS
  DEP_FILE = ijkplayer\CMakeFiles\ijkplayer.dir\ijkavformat\ijkio.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijkplayer\CMakeFiles\ijkplayer.dir
  OBJECT_FILE_DIR = ijkplayer\CMakeFiles\ijkplayer.dir\ijkavformat
  TARGET_COMPILE_PDB = ijkplayer\CMakeFiles\ijkplayer.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijkplayer.pdb

build ijkplayer/CMakeFiles/ijkplayer.dir/ijkavformat/ijkiomanager.c.o: C_COMPILER__ijkplayer D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/ijkavformat/ijkiomanager.c || cmake_object_order_depends_target_ijkplayer
  DEFINES = -DOHOS_PLATFORM -Dijkplayer_EXPORTS
  DEP_FILE = ijkplayer\CMakeFiles\ijkplayer.dir\ijkavformat\ijkiomanager.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijkplayer\CMakeFiles\ijkplayer.dir
  OBJECT_FILE_DIR = ijkplayer\CMakeFiles\ijkplayer.dir\ijkavformat
  TARGET_COMPILE_PDB = ijkplayer\CMakeFiles\ijkplayer.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijkplayer.pdb

build ijkplayer/CMakeFiles/ijkplayer.dir/ijkavformat/ijkiocache.c.o: C_COMPILER__ijkplayer D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/ijkavformat/ijkiocache.c || cmake_object_order_depends_target_ijkplayer
  DEFINES = -DOHOS_PLATFORM -Dijkplayer_EXPORTS
  DEP_FILE = ijkplayer\CMakeFiles\ijkplayer.dir\ijkavformat\ijkiocache.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijkplayer\CMakeFiles\ijkplayer.dir
  OBJECT_FILE_DIR = ijkplayer\CMakeFiles\ijkplayer.dir\ijkavformat
  TARGET_COMPILE_PDB = ijkplayer\CMakeFiles\ijkplayer.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijkplayer.pdb

build ijkplayer/CMakeFiles/ijkplayer.dir/ijkavformat/ijkioffio.c.o: C_COMPILER__ijkplayer D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/ijkavformat/ijkioffio.c || cmake_object_order_depends_target_ijkplayer
  DEFINES = -DOHOS_PLATFORM -Dijkplayer_EXPORTS
  DEP_FILE = ijkplayer\CMakeFiles\ijkplayer.dir\ijkavformat\ijkioffio.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijkplayer\CMakeFiles\ijkplayer.dir
  OBJECT_FILE_DIR = ijkplayer\CMakeFiles\ijkplayer.dir\ijkavformat
  TARGET_COMPILE_PDB = ijkplayer\CMakeFiles\ijkplayer.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijkplayer.pdb

build ijkplayer/CMakeFiles/ijkplayer.dir/ijkavformat/ijkioprotocol.c.o: C_COMPILER__ijkplayer D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/ijkavformat/ijkioprotocol.c || cmake_object_order_depends_target_ijkplayer
  DEFINES = -DOHOS_PLATFORM -Dijkplayer_EXPORTS
  DEP_FILE = ijkplayer\CMakeFiles\ijkplayer.dir\ijkavformat\ijkioprotocol.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijkplayer\CMakeFiles\ijkplayer.dir
  OBJECT_FILE_DIR = ijkplayer\CMakeFiles\ijkplayer.dir\ijkavformat
  TARGET_COMPILE_PDB = ijkplayer\CMakeFiles\ijkplayer.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijkplayer.pdb

build ijkplayer/CMakeFiles/ijkplayer.dir/ijkavformat/ijkioapplication.c.o: C_COMPILER__ijkplayer D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/ijkavformat/ijkioapplication.c || cmake_object_order_depends_target_ijkplayer
  DEFINES = -DOHOS_PLATFORM -Dijkplayer_EXPORTS
  DEP_FILE = ijkplayer\CMakeFiles\ijkplayer.dir\ijkavformat\ijkioapplication.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijkplayer\CMakeFiles\ijkplayer.dir
  OBJECT_FILE_DIR = ijkplayer\CMakeFiles\ijkplayer.dir\ijkavformat
  TARGET_COMPILE_PDB = ijkplayer\CMakeFiles\ijkplayer.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijkplayer.pdb

build ijkplayer/CMakeFiles/ijkplayer.dir/ijkavformat/ijkiourlhook.c.o: C_COMPILER__ijkplayer D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/ijkavformat/ijkiourlhook.c || cmake_object_order_depends_target_ijkplayer
  DEFINES = -DOHOS_PLATFORM -Dijkplayer_EXPORTS
  DEP_FILE = ijkplayer\CMakeFiles\ijkplayer.dir\ijkavformat\ijkiourlhook.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijkplayer\CMakeFiles\ijkplayer.dir
  OBJECT_FILE_DIR = ijkplayer\CMakeFiles\ijkplayer.dir\ijkavformat
  TARGET_COMPILE_PDB = ijkplayer\CMakeFiles\ijkplayer.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijkplayer.pdb

build ijkplayer/CMakeFiles/ijkplayer.dir/ijkavformat/ijkasync.c.o: C_COMPILER__ijkplayer D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/ijkavformat/ijkasync.c || cmake_object_order_depends_target_ijkplayer
  DEFINES = -DOHOS_PLATFORM -Dijkplayer_EXPORTS
  DEP_FILE = ijkplayer\CMakeFiles\ijkplayer.dir\ijkavformat\ijkasync.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijkplayer\CMakeFiles\ijkplayer.dir
  OBJECT_FILE_DIR = ijkplayer\CMakeFiles\ijkplayer.dir\ijkavformat
  TARGET_COMPILE_PDB = ijkplayer\CMakeFiles\ijkplayer.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijkplayer.pdb

build ijkplayer/CMakeFiles/ijkplayer.dir/ijkavformat/ijkurlhook.c.o: C_COMPILER__ijkplayer D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/ijkavformat/ijkurlhook.c || cmake_object_order_depends_target_ijkplayer
  DEFINES = -DOHOS_PLATFORM -Dijkplayer_EXPORTS
  DEP_FILE = ijkplayer\CMakeFiles\ijkplayer.dir\ijkavformat\ijkurlhook.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijkplayer\CMakeFiles\ijkplayer.dir
  OBJECT_FILE_DIR = ijkplayer\CMakeFiles\ijkplayer.dir\ijkavformat
  TARGET_COMPILE_PDB = ijkplayer\CMakeFiles\ijkplayer.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijkplayer.pdb

build ijkplayer/CMakeFiles/ijkplayer.dir/ijkavformat/ijklongurl.c.o: C_COMPILER__ijkplayer D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/ijkavformat/ijklongurl.c || cmake_object_order_depends_target_ijkplayer
  DEFINES = -DOHOS_PLATFORM -Dijkplayer_EXPORTS
  DEP_FILE = ijkplayer\CMakeFiles\ijkplayer.dir\ijkavformat\ijklongurl.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijkplayer\CMakeFiles\ijkplayer.dir
  OBJECT_FILE_DIR = ijkplayer\CMakeFiles\ijkplayer.dir\ijkavformat
  TARGET_COMPILE_PDB = ijkplayer\CMakeFiles\ijkplayer.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijkplayer.pdb

build ijkplayer/CMakeFiles/ijkplayer.dir/ijkavformat/ijksegment.c.o: C_COMPILER__ijkplayer D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/ijkavformat/ijksegment.c || cmake_object_order_depends_target_ijkplayer
  DEFINES = -DOHOS_PLATFORM -Dijkplayer_EXPORTS
  DEP_FILE = ijkplayer\CMakeFiles\ijkplayer.dir\ijkavformat\ijksegment.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijkplayer\CMakeFiles\ijkplayer.dir
  OBJECT_FILE_DIR = ijkplayer\CMakeFiles\ijkplayer.dir\ijkavformat
  TARGET_COMPILE_PDB = ijkplayer\CMakeFiles\ijkplayer.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijkplayer.pdb

build ijkplayer/CMakeFiles/ijkplayer.dir/ijkavutil/ijkdict.c.o: C_COMPILER__ijkplayer D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/ijkavutil/ijkdict.c || cmake_object_order_depends_target_ijkplayer
  DEFINES = -DOHOS_PLATFORM -Dijkplayer_EXPORTS
  DEP_FILE = ijkplayer\CMakeFiles\ijkplayer.dir\ijkavutil\ijkdict.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijkplayer\CMakeFiles\ijkplayer.dir
  OBJECT_FILE_DIR = ijkplayer\CMakeFiles\ijkplayer.dir\ijkavutil
  TARGET_COMPILE_PDB = ijkplayer\CMakeFiles\ijkplayer.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijkplayer.pdb

build ijkplayer/CMakeFiles/ijkplayer.dir/ijkavutil/ijkutils.c.o: C_COMPILER__ijkplayer D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/ijkavutil/ijkutils.c || cmake_object_order_depends_target_ijkplayer
  DEFINES = -DOHOS_PLATFORM -Dijkplayer_EXPORTS
  DEP_FILE = ijkplayer\CMakeFiles\ijkplayer.dir\ijkavutil\ijkutils.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijkplayer\CMakeFiles\ijkplayer.dir
  OBJECT_FILE_DIR = ijkplayer\CMakeFiles\ijkplayer.dir\ijkavutil
  TARGET_COMPILE_PDB = ijkplayer\CMakeFiles\ijkplayer.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijkplayer.pdb

build ijkplayer/CMakeFiles/ijkplayer.dir/ijkavutil/ijkthreadpool.c.o: C_COMPILER__ijkplayer D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/ijkavutil/ijkthreadpool.c || cmake_object_order_depends_target_ijkplayer
  DEFINES = -DOHOS_PLATFORM -Dijkplayer_EXPORTS
  DEP_FILE = ijkplayer\CMakeFiles\ijkplayer.dir\ijkavutil\ijkthreadpool.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijkplayer\CMakeFiles\ijkplayer.dir
  OBJECT_FILE_DIR = ijkplayer\CMakeFiles\ijkplayer.dir\ijkavutil
  TARGET_COMPILE_PDB = ijkplayer\CMakeFiles\ijkplayer.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijkplayer.pdb

build ijkplayer/CMakeFiles/ijkplayer.dir/ijkavutil/ijktree.c.o: C_COMPILER__ijkplayer D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/ijkavutil/ijktree.c || cmake_object_order_depends_target_ijkplayer
  DEFINES = -DOHOS_PLATFORM -Dijkplayer_EXPORTS
  DEP_FILE = ijkplayer\CMakeFiles\ijkplayer.dir\ijkavutil\ijktree.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijkplayer\CMakeFiles\ijkplayer.dir
  OBJECT_FILE_DIR = ijkplayer\CMakeFiles\ijkplayer.dir\ijkavutil
  TARGET_COMPILE_PDB = ijkplayer\CMakeFiles\ijkplayer.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijkplayer.pdb

build ijkplayer/CMakeFiles/ijkplayer.dir/ijkavutil/ijkfifo.c.o: C_COMPILER__ijkplayer D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/ijkavutil/ijkfifo.c || cmake_object_order_depends_target_ijkplayer
  DEFINES = -DOHOS_PLATFORM -Dijkplayer_EXPORTS
  DEP_FILE = ijkplayer\CMakeFiles\ijkplayer.dir\ijkavutil\ijkfifo.c.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -Wno-int-conversion -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijkplayer\CMakeFiles\ijkplayer.dir
  OBJECT_FILE_DIR = ijkplayer\CMakeFiles\ijkplayer.dir\ijkavutil
  TARGET_COMPILE_PDB = ijkplayer\CMakeFiles\ijkplayer.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijkplayer.pdb

build ijkplayer/CMakeFiles/ijkplayer.dir/ijkavutil/ijkstl.cpp.o: CXX_COMPILER__ijkplayer D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/ijkavutil/ijkstl.cpp || cmake_object_order_depends_target_ijkplayer
  DEFINES = -DOHOS_PLATFORM -Dijkplayer_EXPORTS
  DEP_FILE = ijkplayer\CMakeFiles\ijkplayer.dir\ijkavutil\ijkstl.cpp.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security  -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijkplayer\CMakeFiles\ijkplayer.dir
  OBJECT_FILE_DIR = ijkplayer\CMakeFiles\ijkplayer.dir\ijkavutil
  TARGET_COMPILE_PDB = ijkplayer\CMakeFiles\ijkplayer.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijkplayer.pdb

build ijkplayer/CMakeFiles/ijkplayer.dir/ohos/ffpipenode_ohos_mediacodec_vdec.cpp.o: CXX_COMPILER__ijkplayer D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/ohos/ffpipenode_ohos_mediacodec_vdec.cpp || cmake_object_order_depends_target_ijkplayer
  DEFINES = -DOHOS_PLATFORM -Dijkplayer_EXPORTS
  DEP_FILE = ijkplayer\CMakeFiles\ijkplayer.dir\ohos\ffpipenode_ohos_mediacodec_vdec.cpp.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security  -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijkplayer\CMakeFiles\ijkplayer.dir
  OBJECT_FILE_DIR = ijkplayer\CMakeFiles\ijkplayer.dir\ohos
  TARGET_COMPILE_PDB = ijkplayer\CMakeFiles\ijkplayer.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijkplayer.pdb

build ijkplayer/CMakeFiles/ijkplayer.dir/ohos/ohos_video_decoder_data.cpp.o: CXX_COMPILER__ijkplayer D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/ohos/ohos_video_decoder_data.cpp || cmake_object_order_depends_target_ijkplayer
  DEFINES = -DOHOS_PLATFORM -Dijkplayer_EXPORTS
  DEP_FILE = ijkplayer\CMakeFiles\ijkplayer.dir\ohos\ohos_video_decoder_data.cpp.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security  -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijkplayer\CMakeFiles\ijkplayer.dir
  OBJECT_FILE_DIR = ijkplayer\CMakeFiles\ijkplayer.dir\ohos
  TARGET_COMPILE_PDB = ijkplayer\CMakeFiles\ijkplayer.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijkplayer.pdb

build ijkplayer/CMakeFiles/ijkplayer.dir/ohos/ohos_video_decoder_Info.cpp.o: CXX_COMPILER__ijkplayer D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/ohos/ohos_video_decoder_Info.cpp || cmake_object_order_depends_target_ijkplayer
  DEFINES = -DOHOS_PLATFORM -Dijkplayer_EXPORTS
  DEP_FILE = ijkplayer\CMakeFiles\ijkplayer.dir\ohos\ohos_video_decoder_Info.cpp.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security  -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijkplayer\CMakeFiles\ijkplayer.dir
  OBJECT_FILE_DIR = ijkplayer\CMakeFiles\ijkplayer.dir\ohos
  TARGET_COMPILE_PDB = ijkplayer\CMakeFiles\ijkplayer.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijkplayer.pdb

build ijkplayer/CMakeFiles/ijkplayer.dir/ohos/ohos_video_decoder.cpp.o: CXX_COMPILER__ijkplayer D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/ohos/ohos_video_decoder.cpp || cmake_object_order_depends_target_ijkplayer
  DEFINES = -DOHOS_PLATFORM -Dijkplayer_EXPORTS
  DEP_FILE = ijkplayer\CMakeFiles\ijkplayer.dir\ohos\ohos_video_decoder.cpp.o.d
  FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security  -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -O2 -DNDEBUG  -fPIC
  INCLUDES = -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/x86_64/include -ID:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/yuv/x86_64/include
  OBJECT_DIR = ijkplayer\CMakeFiles\ijkplayer.dir
  OBJECT_FILE_DIR = ijkplayer\CMakeFiles\ijkplayer.dir\ohos
  TARGET_COMPILE_PDB = ijkplayer\CMakeFiles\ijkplayer.dir\
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijkplayer.pdb


# =============================================================================
# Link build statements for SHARED_LIBRARY target ijkplayer


#############################################
# Link the shared library D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijkplayer.so

build D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/build/default/intermediates/cmake/default/obj/x86_64/libijkplayer.so: CXX_SHARED_LIBRARY_LINKER__ijkplayer ijkplayer/CMakeFiles/ijkplayer.dir/ff_cmdutils.c.o ijkplayer/CMakeFiles/ijkplayer.dir/ff_ffplay.c.o ijkplayer/CMakeFiles/ijkplayer.dir/ff_ffpipeline.c.o ijkplayer/CMakeFiles/ijkplayer.dir/ff_ffpipenode.c.o ijkplayer/CMakeFiles/ijkplayer.dir/ijkmeta.c.o ijkplayer/CMakeFiles/ijkplayer.dir/ijkplayer.c.o ijkplayer/CMakeFiles/ijkplayer.dir/ijkplayer_android.c.o ijkplayer/CMakeFiles/ijkplayer.dir/pipeline/ffpipenode_ffplay_vdec.c.o ijkplayer/CMakeFiles/ijkplayer.dir/pipeline/ffpipeline_android.c.o ijkplayer/CMakeFiles/ijkplayer.dir/ijkavformat/allformats.c.o ijkplayer/CMakeFiles/ijkplayer.dir/ijkavformat/ijklivehook.c.o ijkplayer/CMakeFiles/ijkplayer.dir/ijkavformat/ijkio.c.o ijkplayer/CMakeFiles/ijkplayer.dir/ijkavformat/ijkiomanager.c.o ijkplayer/CMakeFiles/ijkplayer.dir/ijkavformat/ijkiocache.c.o ijkplayer/CMakeFiles/ijkplayer.dir/ijkavformat/ijkioffio.c.o ijkplayer/CMakeFiles/ijkplayer.dir/ijkavformat/ijkioprotocol.c.o ijkplayer/CMakeFiles/ijkplayer.dir/ijkavformat/ijkioapplication.c.o ijkplayer/CMakeFiles/ijkplayer.dir/ijkavformat/ijkiourlhook.c.o ijkplayer/CMakeFiles/ijkplayer.dir/ijkavformat/ijkasync.c.o ijkplayer/CMakeFiles/ijkplayer.dir/ijkavformat/ijkurlhook.c.o ijkplayer/CMakeFiles/ijkplayer.dir/ijkavformat/ijklongurl.c.o ijkplayer/CMakeFiles/ijkplayer.dir/ijkavformat/ijksegment.c.o ijkplayer/CMakeFiles/ijkplayer.dir/ijkavutil/ijkdict.c.o ijkplayer/CMakeFiles/ijkplayer.dir/ijkavutil/ijkutils.c.o ijkplayer/CMakeFiles/ijkplayer.dir/ijkavutil/ijkthreadpool.c.o ijkplayer/CMakeFiles/ijkplayer.dir/ijkavutil/ijktree.c.o ijkplayer/CMakeFiles/ijkplayer.dir/ijkavutil/ijkfifo.c.o ijkplayer/CMakeFiles/ijkplayer.dir/ijkavutil/ijkstl.cpp.o ijkplayer/CMakeFiles/ijkplayer.dir/ohos/ffpipenode_ohos_mediacodec_vdec.cpp.o ijkplayer/CMakeFiles/ijkplayer.dir/ohos/ohos_video_decoder_data.cpp.o ijkplayer/CMakeFiles/ijkplayer.dir/ohos/ohos_video_decoder_Info.cpp.o ijkplayer/CMakeFiles/ijkplayer.dir/ohos/ohos_video_decoder.cpp.o | D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/build/default/intermediates/cmake/default/obj/x86_64/libijksdl.so D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/x86_64/lib/libsoundtouch.a
  LANGUAGE_COMPILE_FLAGS = -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security  -s -D__MUSL__ -Wno-int-conversion -Wl,-Bsymbolic -O2 -DNDEBUG
  LINK_FLAGS = --rtlib=compiler-rt -fuse-ld=lld -Wl,--build-id=sha1 -Wl,--warn-shared-textrel -Wl,--fatal-warnings -lunwind -Wl,--no-undefined -Qunused-arguments -Wl,-z,noexecstack
  LINK_LIBRARIES = -Wl,-rpath,D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/x86_64/lib:D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/x86_64/lib:D:/vp8/ohos_ijkplayer-vp8/ijkplayer/build/default/intermediates/cmake/default/obj/x86_64  D:/vp8/ohos_ijkplayer-vp8/ijkplayer/build/default/intermediates/cmake/default/obj/x86_64/libijksdl.so  -lEGL  -lGLESv3  -lhilog_ndk.z  D:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/soundtouch/x86_64/lib/libsoundtouch.a  -lz  -lavcodec  -lavfilter  -lavformat  -lavutil  -lswresample  -lswscale  -lavdevice  -lcrypto  -lssl  -lnative_media_codecbase  -lnative_media_core  -lnative_media_vdec  -lnative_window  -lnative_buffer  -lm
  LINK_PATH = -LD:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/ffmpeg/ffmpeg/x86_64/lib   -LD:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/../third_party/openssl/x86_64/lib
  OBJECT_DIR = ijkplayer\CMakeFiles\ijkplayer.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libijkplayer.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = ijkplayer\CMakeFiles\ijkplayer.dir\
  TARGET_FILE = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijkplayer.so
  TARGET_PDB = D:\vp8\ohos_ijkplayer-vp8\ijkplayer\build\default\intermediates\cmake\default\obj\x86_64\libijkplayer.pdb


#############################################
# Utility command for edit_cache

build ijkplayer/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\vp8\ohos_ijkplayer-vp8\ijkplayer\.cxx\default\default\x86_64\ijkplayer && D:\harmonyFor\openSDK\11\native\build-tools\cmake\bin\cmake-gui.exe -SD:\vp8\ohos_ijkplayer-vp8\ijkplayer\src\main\cpp -BD:\vp8\ohos_ijkplayer-vp8\ijkplayer\.cxx\default\default\x86_64"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build ijkplayer/edit_cache: phony ijkplayer/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build ijkplayer/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\vp8\ohos_ijkplayer-vp8\ijkplayer\.cxx\default\default\x86_64\ijkplayer && D:\harmonyFor\openSDK\11\native\build-tools\cmake\bin\cmake.exe -SD:\vp8\ohos_ijkplayer-vp8\ijkplayer\src\main\cpp -BD:\vp8\ohos_ijkplayer-vp8\ijkplayer\.cxx\default\default\x86_64"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build ijkplayer/rebuild_cache: phony ijkplayer/CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build ijkplayer: phony D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/build/default/intermediates/cmake/default/obj/x86_64/libijkplayer.so

build ijkplayer_audio_napi: phony D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/build/default/intermediates/cmake/default/obj/x86_64/libijkplayer_audio_napi.so

build ijkplayer_napi: phony D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/build/default/intermediates/cmake/default/obj/x86_64/libijkplayer_napi.so

build ijksdl: phony D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/build/default/intermediates/cmake/default/obj/x86_64/libijksdl.so

build libijkplayer.so: phony D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/build/default/intermediates/cmake/default/obj/x86_64/libijkplayer.so

build libijkplayer_audio_napi.so: phony D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/build/default/intermediates/cmake/default/obj/x86_64/libijkplayer_audio_napi.so

build libijkplayer_napi.so: phony D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/build/default/intermediates/cmake/default/obj/x86_64/libijkplayer_napi.so

build libijksdl.so: phony D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/build/default/intermediates/cmake/default/obj/x86_64/libijksdl.so

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: D:/vp8/ohos_ijkplayer-vp8/ijkplayer/.cxx/default/default/x86_64

build all: phony D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/build/default/intermediates/cmake/default/obj/x86_64/libijkplayer_napi.so D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/build/default/intermediates/cmake/default/obj/x86_64/libijkplayer_audio_napi.so ijksdl/all ijkplayer/all

# =============================================================================

#############################################
# Folder: D:/vp8/ohos_ijkplayer-vp8/ijkplayer/.cxx/default/default/x86_64/ijkplayer

build ijkplayer/all: phony D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/build/default/intermediates/cmake/default/obj/x86_64/libijkplayer.so

# =============================================================================

#############################################
# Folder: D:/vp8/ohos_ijkplayer-vp8/ijkplayer/.cxx/default/default/x86_64/ijksdl

build ijksdl/all: phony D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/build/default/intermediates/cmake/default/obj/x86_64/libijksdl.so

# =============================================================================
# Built-in targets


#############################################
# Make the all target the default.

default all

#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | CMakeCache.txt CMakeFiles/3.16.5/CMakeCCompiler.cmake CMakeFiles/3.16.5/CMakeCXXCompiler.cmake CMakeFiles/3.16.5/CMakeSystem.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeCCompiler.cmake.in D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeCCompilerABI.c D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeCInformation.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeCXXCompiler.cmake.in D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeCXXCompilerABI.cpp D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeCXXInformation.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeCommonLanguageInclude.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeCompilerIdDetection.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeDetermineCCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeDetermineCXXCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeDetermineCompileFeatures.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeDetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeDetermineCompilerABI.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeDetermineCompilerId.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeDetermineSystem.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeFindBinUtils.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeGenericSystem.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeInitializeConfigs.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeLanguageInformation.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeParseImplicitIncludeInfo.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeParseImplicitLinkInfo.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeSystem.cmake.in D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeSystemSpecificInformation.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeSystemSpecificInitialize.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeTestCCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeTestCXXCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeTestCompilerCommon.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/ADSP-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/ARMCC-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/ARMClang-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/AppleClang-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Borland-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Bruce-C-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/CMakeCommonCompilerMacros.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Clang-C.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Clang-CXX.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Clang-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Clang-DetermineCompilerInternal.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Clang-FindBinUtils.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Clang.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Compaq-C-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Cray-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Embarcadero-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Fujitsu-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/GHS-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/GNU-C-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/GNU.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/HP-C-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/HP-CXX-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/IAR-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Intel-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/MSVC-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/NVIDIA-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/PGI-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/PathScale-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/SCO-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/SDCC-C-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/SunPro-C-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/TI-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Watcom-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/XL-C-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/XL-CXX-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/XLClang-C-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/zOS-C-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Internal/CMakeCheckCompilerFlag.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Internal/FeatureTesting.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Platform/Linux.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Platform/OHOS.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Platform/UnixPaths.cmake D$:/harmonyFor/openSDK/11/native/build/cmake/ohos.toolchain.cmake D$:/harmonyFor/openSDK/11/native/build/cmake/sdk_native_platforms.cmake D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/CMakeLists.txt D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/CMakeLists.txt D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/CMakeLists.txt
  pool = console


#############################################
# A missing CMake input file is not an error.

build CMakeCache.txt CMakeFiles/3.16.5/CMakeCCompiler.cmake CMakeFiles/3.16.5/CMakeCXXCompiler.cmake CMakeFiles/3.16.5/CMakeSystem.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeCCompiler.cmake.in D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeCCompilerABI.c D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeCInformation.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeCXXCompiler.cmake.in D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeCXXCompilerABI.cpp D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeCXXInformation.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeCommonLanguageInclude.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeCompilerIdDetection.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeDetermineCCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeDetermineCXXCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeDetermineCompileFeatures.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeDetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeDetermineCompilerABI.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeDetermineCompilerId.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeDetermineSystem.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeFindBinUtils.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeGenericSystem.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeInitializeConfigs.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeLanguageInformation.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeParseImplicitIncludeInfo.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeParseImplicitLinkInfo.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeSystem.cmake.in D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeSystemSpecificInformation.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeSystemSpecificInitialize.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeTestCCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeTestCXXCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/CMakeTestCompilerCommon.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/ADSP-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/ARMCC-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/ARMClang-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/AppleClang-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Borland-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Bruce-C-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/CMakeCommonCompilerMacros.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Clang-C.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Clang-CXX.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Clang-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Clang-DetermineCompilerInternal.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Clang-FindBinUtils.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Clang.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Compaq-C-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Cray-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Embarcadero-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Fujitsu-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/GHS-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/GNU-C-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/GNU.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/HP-C-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/HP-CXX-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/IAR-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Intel-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/MSVC-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/NVIDIA-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/PGI-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/PathScale-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/SCO-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/SDCC-C-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/SunPro-C-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/TI-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/Watcom-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/XL-C-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/XL-CXX-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/XLClang-C-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/zOS-C-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Internal/CMakeCheckCompilerFlag.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Internal/FeatureTesting.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Platform/Linux.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Platform/OHOS.cmake D$:/harmonyFor/openSDK/11/native/build-tools/cmake/share/cmake-3.16/Modules/Platform/UnixPaths.cmake D$:/harmonyFor/openSDK/11/native/build/cmake/ohos.toolchain.cmake D$:/harmonyFor/openSDK/11/native/build/cmake/sdk_native_platforms.cmake D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/CMakeLists.txt D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijkplayer/CMakeLists.txt D$:/vp8/ohos_ijkplayer-vp8/ijkplayer/src/main/cpp/ijksdl/CMakeLists.txt: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP

