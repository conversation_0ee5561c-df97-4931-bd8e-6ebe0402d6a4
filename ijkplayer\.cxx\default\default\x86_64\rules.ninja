# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.16

# This file contains all the rules used to get the outputs files
# built from the input files.
# It is included in the main 'build.ninja'.

# =============================================================================
# Project: ijkplayer
# Configuration: Release
# =============================================================================
# =============================================================================

#############################################
# Rule for compiling C files.

rule C_COMPILER__ijkplayer_napi
  depfile = $DEP_FILE
  deps = gcc
  command = D:\harmonyFor\openSDK\11\native\llvm\bin\clang.exe --target=x86_64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out   -c $in
  description = Building C object $out


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__ijkplayer_napi
  depfile = $DEP_FILE
  deps = gcc
  command = D:\harmonyFor\openSDK\11\native\llvm\bin\clang++.exe --target=x86_64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot  $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for linking CXX shared library.

rule CXX_SHARED_LIBRARY_LINKER__ijkplayer_napi
  command = cmd.exe /C "$PRE_LINK && D:\harmonyFor\openSDK\11\native\llvm\bin\clang++.exe --target=x86_64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -fPIC $LANGUAGE_COMPILE_FLAGS $ARCH_FLAGS $LINK_FLAGS -shared $SONAME_FLAG$SONAME -o $TARGET_FILE $in $LINK_PATH $LINK_LIBRARIES && $POST_BUILD"
  description = Linking CXX shared library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__ijkplayer_audio_napi
  depfile = $DEP_FILE
  deps = gcc
  command = D:\harmonyFor\openSDK\11\native\llvm\bin\clang.exe --target=x86_64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out   -c $in
  description = Building C object $out


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__ijkplayer_audio_napi
  depfile = $DEP_FILE
  deps = gcc
  command = D:\harmonyFor\openSDK\11\native\llvm\bin\clang++.exe --target=x86_64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot  $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for linking CXX shared library.

rule CXX_SHARED_LIBRARY_LINKER__ijkplayer_audio_napi
  command = cmd.exe /C "$PRE_LINK && D:\harmonyFor\openSDK\11\native\llvm\bin\clang++.exe --target=x86_64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -fPIC $LANGUAGE_COMPILE_FLAGS $ARCH_FLAGS $LINK_FLAGS -shared $SONAME_FLAG$SONAME -o $TARGET_FILE $in $LINK_PATH $LINK_LIBRARIES && $POST_BUILD"
  description = Linking CXX shared library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for running custom commands.

rule CUSTOM_COMMAND
  command = $COMMAND
  description = $DESC


#############################################
# Rule for compiling C files.

rule C_COMPILER__ijksdl
  depfile = $DEP_FILE
  deps = gcc
  command = D:\harmonyFor\openSDK\11\native\llvm\bin\clang.exe --target=x86_64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out   -c $in
  description = Building C object $out


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__ijksdl
  depfile = $DEP_FILE
  deps = gcc
  command = D:\harmonyFor\openSDK\11\native\llvm\bin\clang++.exe --target=x86_64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot  $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for linking CXX shared library.

rule CXX_SHARED_LIBRARY_LINKER__ijksdl
  command = cmd.exe /C "$PRE_LINK && D:\harmonyFor\openSDK\11\native\llvm\bin\clang++.exe --target=x86_64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -fPIC $LANGUAGE_COMPILE_FLAGS $ARCH_FLAGS $LINK_FLAGS -shared $SONAME_FLAG$SONAME -o $TARGET_FILE $in $LINK_PATH $LINK_LIBRARIES && $POST_BUILD"
  description = Linking CXX shared library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__ijkplayer
  depfile = $DEP_FILE
  deps = gcc
  command = D:\harmonyFor\openSDK\11\native\llvm\bin\clang.exe --target=x86_64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out   -c $in
  description = Building C object $out


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__ijkplayer
  depfile = $DEP_FILE
  deps = gcc
  command = D:\harmonyFor\openSDK\11\native\llvm\bin\clang++.exe --target=x86_64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot  $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for linking CXX shared library.

rule CXX_SHARED_LIBRARY_LINKER__ijkplayer
  command = cmd.exe /C "$PRE_LINK && D:\harmonyFor\openSDK\11\native\llvm\bin\clang++.exe --target=x86_64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot -fPIC $LANGUAGE_COMPILE_FLAGS $ARCH_FLAGS $LINK_FLAGS -shared $SONAME_FLAG$SONAME -o $TARGET_FILE $in $LINK_PATH $LINK_LIBRARIES && $POST_BUILD"
  description = Linking CXX shared library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for re-running cmake.

rule RERUN_CMAKE
  command = D:\harmonyFor\openSDK\11\native\build-tools\cmake\bin\cmake.exe -SD:\vp8\ohos_ijkplayer-vp8\ijkplayer\src\main\cpp -BD:\vp8\ohos_ijkplayer-vp8\ijkplayer\.cxx\default\default\x86_64
  description = Re-running CMake...
  generator = 1


#############################################
# Rule for cleaning all built files.

rule CLEAN
  command = D:\harmonyFor\openSDK\11\native\build-tools\cmake\bin\ninja.exe -t clean
  description = Cleaning all built files...


#############################################
# Rule for printing all primary targets available.

rule HELP
  command = D:\harmonyFor\openSDK\11\native\build-tools\cmake\bin\ninja.exe -t targets
  description = All primary targets available:

